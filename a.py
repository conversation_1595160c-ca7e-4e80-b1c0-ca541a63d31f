

#!/usr/bin/env python3
# Copyright (C) Alibaba Group. All Rights Reserved.
# MIT License (https://opensource.org/licenses/MIT)

import os
import sys
import time
import dashscope
from dashscope.audio.tts_v2 import *


# 主话术缓存3句, 插入话术无限, 有插入话术就优先读取插入话术

text_to_synthesize = '我的心里呀，就如同喝了一瓶蜜一样甜呢！真心为你开心呢！'
file_to_save = 'result.mp3'


def init_dashscope_api_key():
    '''
    Set your DashScope API-key. More information:
    https://github.com/aliyun/alibabacloud-bailian-speech-demo/blob/master/PREREQUISITES.md
    '''
    if 'DASHSCOPE_API_KEY' in os.environ:
        dashscope.api_key = os.environ[
            'DASHSCOPE_API_KEY']  # load API-key from environment variable DASHSCOPE_API_KEY
    else:
        dashscope.api_key = "sk-8c0dcb0c57014423bf4d67f9f900a117"  # set API-key manually


def synthesize_speech_from_text(text, file_path):
    '''
    Synthesize speech with given text, sync call and save the audio data into file_path
    For more information, please refer to https://help.aliyun.com/document_detail/2712523.html
    '''
    # Initialize the speech synthesizer
    # you can customize the synthesis parameters, like voice, format, sample_rate or other parameters
    speech_synthesizer = SpeechSynthesizer(model='cosyvoice-v2',
                                           voice='longhua_v2',  # cosyvoice-v2-demo-17ff68236e47465ca2323d87d1fe0689 longhua_v2
                                           callback=None)
    t1 = time.time()
    
    audio = speech_synthesizer.call(text)
    # Save the synthesized audio to a file
    with open(file_path, 'wb') as f:
        f.write(audio)
    print(f'Synthesized text {text} to file : {file_path}')
    print('[Metric] requestId: {}, first package delay ms: {}'.format(
        speech_synthesizer.get_last_request_id(),
        speech_synthesizer.get_first_package_delay()))


    print("use time: {}".format(time.time() - t1))


def create_clone_voice(audio_url: str):
    voice_clone_service = VoiceEnrollmentService()
    print('start cloning your voice...')
    new_voice_id = voice_clone_service.create_voice(
        target_model='cosyvoice-v2', prefix='demo', url=audio_url)
    print('requestId: ', voice_clone_service.get_last_request_id())
    print('voice clone done.')
    print('your new voice is: {}'.format(new_voice_id))
    voices_list = voice_clone_service.list_voices(
        page_index=0,
        page_size=10,
    )
    print('requestId: ', voice_clone_service.get_last_request_id())
    print('your current voices list:')
    for voice in voices_list:
        print(voice)
    return new_voice_id


def delete_voice_by_prefix(prefix):
    voice_clone_service = VoiceEnrollmentService()
    voices_list = voice_clone_service.list_voices(
        prefix=prefix,
        page_index=0,
        page_size=10,
    )
    print('requestId: ', voice_clone_service.get_last_request_id())
    for voice in voices_list:
        voice_id = voice['voice_id']
        voice_clone_service.delete_voice(voice_id)
        print('requestId: ', voice_clone_service.get_last_request_id())
        print(f'voice {voice} deleted')


# main function
if __name__ == '__main__':
    init_dashscope_api_key()
    # 克隆音色
    # if len(sys.argv) < 2:
    #     audio_url = 'https://dashscope.oss-cn-beijing.aliyuncs.com/samples/audio/cosyvoice/210024_happy.wav'
    # else:
    #     audio_url = sys.argv[1]
    # text_to_synthesize = '你好，欢迎使用阿里巴巴通义语音实验室的音色复刻服务～'
    # your_cloned_voice = create_clone_voice(audio_url)
    synthesize_speech_from_text(text=text_to_synthesize,
                                file_path=file_to_save)