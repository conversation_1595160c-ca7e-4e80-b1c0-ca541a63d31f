<html class="pixel-ratio-1"><head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>AI替你播</title><link rel="shortcut icon" href="/favicon.ico" type="image/x-icon">
    <script src="https://hm.baidu.com/hm.js?c346bca0cab9552304cdc699f0dec8bf"></script><script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?c346bca0cab9552304cdc699f0dec8bf";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
</script>
    <style>
    :root {
        --bg-dark: #0D1117;
        --bg-card: #161B22;
        --text-primary: #E6EDF3;
        --text-secondary: #7D8590;
        --accent: #7C3AED;
        --accent-hover: #6D28D9;
        --border: #30363D;
        --success: #238636;
        --bg-secondary: #2D333B;
        --bg-tertiary: #3D434B;
        --accent-tertiary: #6D28D9;
    }

    body {
        margin: 0;
        padding: 0;
        background: var(--bg-dark);
        color: var(--text-primary);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    }

    .app-container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 2rem;
    }

    /* 数字主播信息卡片 */
    .digital-host-card {
        background: var(--bg-card);
        border-radius: 12px;
        border: 1px solid var(--border);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .host-header {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    /* 用户头像样式 */
    .host-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: #64ffda;
        color: #1a1c1e;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: bold;
    }

    .host-info {
        flex: 1;
    }

    .host-name {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 4px;
    }

    .host-status {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .vip-badge {
        background: linear-gradient(45deg, #ffd700, #ffa500);
        color: #1a1c1e;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
    }

    .expire-date {
        color: var(--text-secondary);
        font-size: 12px;
    }

    .normal-badge {
        background: rgba(255,255,255,0.1);
        color: var(--text-secondary);
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
    }

    /* 主要内容区域 */
    .main-content {
        display: flex;
        flex-direction: column;
        gap: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }

    /* 输入区域 - 放在顶部，两列布局 */
    .input-section {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        margin-bottom: 20px;
    }

    /* 控制面板 - 放在下方 */
    .control-panel {
        background: rgba(22, 27, 34, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 20px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }

    @media screen and (max-width: 768px) {
        .control-panel,
        .input-section {
            grid-template-columns: 1fr;
        }
    }

    /* 左侧输入区域 */
    .input-section {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    /* 输入框样式优化 */
    .input-box {
        background: linear-gradient(145deg, rgba(22, 27, 34, 0.8), rgba(13, 17, 23, 0.9));
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    /* 标题样式 */
    .input-header {
        display: flex;
        align-items: center;
        gap: 0.8rem;
        margin-bottom: 1rem;
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
        font-weight: 500;
    }

    .input-header i {
        color: #ffd700;
        font-size: 16px;
    }

    /* 文本框样式 */
    textarea {
        width: 100%;
        min-height: 120px;
        background: rgba(13, 17, 23, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        color: rgba(255, 255, 255, 0.85);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
        font-size: 12px;
        line-height: 1.8;
        letter-spacing: 0.3px;
        resize: vertical;
        transition: all 0.3s ease;
    }

    textarea:focus {
        outline: none;
        border-color: rgba(100, 255, 218, 0.3);
        box-shadow: 0 0 0 2px rgba(100, 255, 218, 0.1);
        background: rgba(13, 17, 23, 0.8);
    }

    /* 占位符样式 */
    textarea::placeholder {
        color: rgba(255, 255, 255, 0.3);
    }

    /* 滚动条样式 */
    textarea::-webkit-scrollbar {
        width: 8px;
    }

    textarea::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 4px;
    }

    textarea::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
    }

    textarea::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.2);
    }

    /* 特殊要求标题样式 */
    .special-requirements {
        color: #ffd700;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .special-requirements::before {
        content: '✨';
        font-size: 16px;
    }

    /* 文本选中样式 */
    textarea::selection {
        background: rgba(100, 255, 218, 0.2);
        color: #fff;
    }

    /* 会员信息显示 */
    .member-info {
        display: flex;
        justify-content: space-between;
        padding: 1rem;
        background: rgba(124, 58, 237, 0.1);
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .member-item {
        text-align: center;
    }

    .member-label {
        font-size: 0.8rem;
        color: var(--text-secondary);
    }

    .member-value {
        font-size: 1rem;
        color: var(--accent);
        margin-top: 0.2rem;
    }

    /* 按钮样式 */
    .btn-primary {
        width: 100%;
        padding: 0.8rem;
        border-radius: 8px;
        border: none;
        background: var(--accent);
        color: white;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
    }

    .btn-primary:hover {
        background: var(--accent-hover);
        transform: translateY(-1px);
    }

    .btn-primary:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* 语音选择器 */
    select {
        width: 100%;
        padding: 0.8rem;
        background: rgba(13, 17, 23, 0.5);
        border: 1px solid var(--border);
        border-radius: 8px;
        color: var(--text-primary);
        cursor: pointer;
    }

    audio {
        width: 100%;
        margin-top: 1rem;
    }

    /* 设置区域样式 */
    .setting-section {
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border);
    }

    .setting-section:last-child {
        border-bottom: none;
    }

    .setting-title {
        font-size: 0.9rem;
        color: var(--text-secondary);
        margin-bottom: 1rem;
    }

    .setting-item {
        margin-bottom: 0.8rem;
        padding: 0.5rem;
        background: rgba(13, 17, 23, 0.3);
        border-radius: 8px;
    }

    /* 开关样式 */
    .toggle {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
    }

    .toggle input[type="checkbox"] {
        width: 16px;
        height: 16px;
        accent-color: var(--accent);
    }

    /* 滑块样式 */
    input[type="range"] {
        width: 100%;
        height: 4px;
        background: var(--border);
        border-radius: 2px;
        accent-color: var(--accent);
    }

    /* 输入框样式 */
    .input-styled {
        width: 100%;
        padding: 0.8rem;
        background: rgba(13, 17, 23, 0.5);
        border: 1px solid var(--border);
        border-radius: 8px;
        color: var(--text-primary);
    }

    /* 次要按钮样式 */
    .btn-secondary {
        width: 100%;
        padding: 0.8rem;
        background: transparent;
        border: 1px solid var(--border);
        border-radius: 8px;
        color: var(--text-primary);
        cursor: pointer;
        transition: all 0.2s;
    }

    .btn-secondary:hover {
        border-color: var(--accent);
        color: var(--accent);
    }

    /* 单选框样式 */
    .radio {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-right: 1rem;
        cursor: pointer;
    }

    .radio input[type="radio"] {
        accent-color: var(--accent);
    }

    /* 添加禁用状态下单选按钮的样式 */
    input[type="radio"]:disabled {
        opacity: 0.6;
    }
    
    /* 确保禁用状态下选中的单选按钮更明显 */
    input[type="radio"]:checked:disabled {
        opacity: 0.8;
        box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.5);
    }
    
    /* 为单选按钮的父元素添加样式，使选中项更明显 */
    label:has(input[type="radio"]:checked:disabled) {
        color: var(--accent);
        font-weight: bold;
    }

    /* 为不支持:has选择器的浏览器提供备选方案 */
    input[type="radio"]:checked:disabled + span,
    input[type="radio"]:checked:disabled ~ span {
        color: var(--accent);
        font-weight: bold;
    }

    /* 值显示 */
    .value {
        color: var(--text-secondary);
        font-size: 0.9rem;
        margin-left: 0.5rem;
    }

    /* 提示文本 */
    .hint-text {
        color: var(--text-secondary);
        font-size: 0.8rem;
        margin-top: 0.3rem;
        margin-left: 1.8rem;
    }

    /* 设置标签 */
    .setting-label {
        color: var(--text-secondary);
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    /* 单选按钮组 */
    .radio-group {
        display: flex;
        gap: 1rem;
    }

    /* 复选框组 */
    .checkbox-group {
        display: flex;
        gap: 2rem;
    }

    /* 单选按钮和复选框容器 */
    .setting-item {
        margin-bottom: 1.5rem;
        padding: 0.5rem;
        background: rgba(13, 17, 23, 0.3);
        border-radius: 8px;
    }

    /* 聊天工具样式 */
    .chat-tools {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: var(--bg-card);
        border-top: 1px solid var(--border);
        padding: 1rem 2rem;
        display: flex;
        gap: 1rem;
        align-items: center;
		z-index:100;
    }

    .chat-input {
        flex: 1;
    }

    .chat-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .chat-buttons .btn-secondary {
        width: auto;
        padding: 0.8rem 1.5rem;
    }

    /* 会话指示器 */
    .session-indicator {
        position: fixed;
        bottom: 80px;
        right: 20px;
        width: 40px;
        height: 40px;
        background: linear-gradient(145deg, var(--accent), #6D28D9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 1000;
        animation: sessionPulse 2s infinite;
        box-shadow: 0 0 15px rgba(124, 58, 237, 0.3);
    }

    /* 添加呼吸动画关键帧 */
    @keyframes sessionPulse {
        0% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(124, 58, 237, 0.7);
        }
        70% {
            transform: scale(1.1);
            box-shadow: 0 0 0 10px rgba(124, 58, 237, 0);
        }
        100% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(124, 58, 237, 0);
        }
    }

    /* 更新会话数量样式 */
    .session-count {
        color: white;
        font-weight: bold;
        font-size: 16px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        animation: textGlow 2s infinite;
    }

    /* 添加文字发光动画 */
    @keyframes textGlow {
        0% {
            opacity: 0.8;
        }
        50% {
            opacity: 1;
        }
        100% {
            opacity: 0.8;
        }
    }

    /* 会话列表弹窗 */
    .session-list-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1001;
    }

    .modal-content {
        background: var(--bg-card);
        border-radius: 12px;
        width: 400px;
        max-height: 80vh;
        overflow: hidden;
    }

    .modal-header {
        padding: 1rem;
        border-bottom: 1px solid var(--border);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-body {
        padding: 1rem;
        overflow-y: auto;
        max-height: calc(80vh - 60px);
    }

    .session-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.8rem;
        border: 1px solid var(--border);
        border-radius: 8px;
        margin-bottom: 0.5rem;
    }

    .session-info {
        flex: 1;
    }

    .session-name {
        font-weight: 500;
        margin-bottom: 0.2rem;
    }

    .session-time {
        font-size: 0.8rem;
        color: var(--text-secondary);
    }

    .end-session-btn {
        background: transparent;
        border: none;
        color: var(--text-secondary);
        font-size: 1.2rem;
        cursor: pointer;
        padding: 0.5rem;
    }

    .end-session-btn:hover {
        color: #dc3545;
    }

    .no-sessions {
        text-align: center;
        color: var(--text-secondary);
        padding: 2rem;
    }

    /* 充值按钮样式 */
    .recharge-btn {
        position: fixed;
        bottom: 80px;
        right: 80px;
        width: 40px;
        height: 40px;
        background: #dc3545;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 1000;
        transition: all 0.2s;
    }

    .recharge-btn:hover {
        transform: scale(1.1);
    }

    .recharge-icon {
        color: white;
        font-weight: bold;
    }

    .vip-badge {
        background-color: #ffd700;
        color: #000;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.8rem;
    }

    .expire-date {
        color: #7D8590;
        font-size: 0.8rem;
    }

    .normal-badge {
        background-color: #7D8590;
        color: #fff;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.8rem;
    }

    .host-info {
        display: flex;
        flex-direction: column;
        gap: 0.3rem;
    }

    .host-name {
        font-size: 1.1rem;
        font-weight: 500;
    }

    .vip-badge {
        background: linear-gradient(45deg, #FFD700, #FFA500);
        color: #000;
        padding: 0.2rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .normal-badge {
        background: var(--border);
        color: var(--text-secondary);
        padding: 0.2rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
    }

    .expire-date {
        color: var(--text-secondary);
        font-size: 0.8rem;
        margin-left: 0.5rem;
    }

    /* 修改控制面板样式使其更紧凑 */
    .setting-section {
        margin-bottom: 1rem;
        padding-bottom: 1rem;
    }

    .setting-item {
        margin-bottom: 0.8rem;
        padding: 0.5rem;
    }

    /* 添加弹窗样式 */
    .modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1001;
    }

    .modal-content {
        background: var(--bg-card);
        border-radius: 12px;
        width: 500px;
        max-height: 80vh;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .modal-header {
        padding: 1rem;
        border-bottom: 1px solid var(--border);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-body {
        padding: 1rem;
        overflow-y: auto;
        max-height: calc(80vh - 60px);
    }

    .close-btn {
        background: none;
        border: none;
        color: var(--text-secondary);
        font-size: 1.5rem;
        cursor: pointer;
    }

    .close-btn:hover {
        color: var(--text-primary);
    }

    /* 控制面板重新设计 */
    .control-panel {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    /* 面板部分样式 */
    .panel-section {
        background: var(--bg-card);
        border-radius: 12px;
        border: 1px solid var(--border);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .panel-header {
        font-size: 0.9rem;
        color: var(--text-secondary);
        margin-bottom: 1rem;
    }

    /* 设置组样式 */
    .setting-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    /* 设置标签样式 */
    .setting-label {
        font-size: 0.9rem;
        color: var(--text-secondary);
    }

    /* 单选按钮组样式 */
    .radio-buttons {
        display: flex;
        gap: 0.5rem;
    }

    /* 单选按钮样式 */
    .radio-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* 复选框组样式 */
    .checkbox-buttons {
        display: flex;
        gap: 0.5rem;
    }

    /* 复选框样式 */
    .checkbox-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* 充值按钮样式 */
    .btn-recharge {
        background: transparent;
        border: none;
        color: var(--accent);
        font-size: 0.9rem;
        cursor: pointer;
    }

    /* 充值按钮样式 */
    .btn-recharge:hover {
        text-decoration: underline;
    }

    /* 输入框样式 */
    .input-styled {
        width: 100%;
        padding: 0.8rem;
        background: rgba(13, 17, 23, 0.5);
        border: 1px solid var(--border);
        border-radius: 8px;
        color: var(--text-primary);
    }

    /* 语音选择器样式 */
    .voice-select {
        width: 100%;
        padding: 0.8rem;
        background: rgba(13, 17, 23, 0.5);
        border: 1px solid var(--border);
        border-radius: 8px;
        color: var(--text-primary);
    }

    /* 控制面板样式 */
    .control-panel {
        background: var(--bg-card);
        border-radius: 12px;
        border: 1px solid var(--border);
    }

    .panel-section {
        padding: 1rem;
        border-bottom: 1px solid var(--border);
    }

    .panel-section:last-child {
        border-bottom: none;
    }

    .panel-header {
        font-size: 1rem;
        font-weight: 500;
        margin-bottom: 1rem;
        color: var(--text-primary);
    }

    .setting-group {
        margin-bottom: 1rem;
        padding: 0.8rem;
        background: rgba(13, 17, 23, 0.3);
        border-radius: 8px;
    }

    .setting-group:last-child {
        margin-bottom: 0;
    }

    .radio-buttons, .checkbox-buttons {
        display: flex;
        gap: 1rem;
        margin-top: 0.5rem;
    }

    .radio-button, .checkbox-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
    }

    .power-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 0.5rem;
        padding-top: 0.5rem;
        border-top: 1px solid var(--border);
    }

    .btn-recharge {
        padding: 0.3rem 0.8rem;
        background: var(--accent);
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .voice-select {
        margin-bottom: 0.8rem;
    }

    /* 会员信息卡片样式 - 固定在右上角 */
    .member-info-card {
        position: absolute;
        top: 20px;
        right: 20px;
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 15px;
        background: rgba(22, 27, 34, 0.8);
        border: 1px solid var(--border);
        border-radius: 50px;
        backdrop-filter: blur(10px);
        z-index: 100;
    }

    /* 用户头像 */
    .member-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #64ffda;
        color: #1a1c1e;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: bold;
    }

    /* 会员信息 */
    .member-details {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .member-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-primary);
    }

    .member-status {
        display: flex;
        align-items: center;
        gap: 6px;
    }

    /* VIP标签 */
    .vip-tag {
        background: linear-gradient(45deg, #ffd700, #ffa500);
        color: #1a1c1e;
        padding: 1px 6px;
        border-radius: 3px;
        font-size: 11px;
        font-weight: 600;
    }

    /* 到期时间 */
    .expire-info {
        color: var(--text-secondary);
        font-size: 11px;
    }

    /* 普通用户标签 */
    .normal-tag {
        background: rgba(255,255,255,0.1);
        color: var(--text-secondary);
        padding: 1px 6px;
        border-radius: 3px;
        font-size: 11px;
    }

    /* 余额显示 */
    .balance-info {
        margin-left: 8px;
        padding-left: 8px;
        border-left: 1px solid var(--border);
        font-size: 12px;
        color: var(--text-secondary);
    }

    /* 移动端适配 */
    @media screen and (max-width: 768px) {
        .member-info-card {
            position: relative;
            top: 0;
            right: 0;
            margin: 10px auto;
            width: fit-content;
        }
    }

    /* 主播连接卡片样式 */
    .broadcast-connection-card {
        background: linear-gradient(145deg, rgba(22, 27, 34, 0.8), rgba(13, 17, 23, 0.9));
        backdrop-filter: blur(10px);
        border: 1px solid rgba(100, 255, 218, 0.1);
        border-radius: 16px;
        padding: 1.8rem;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    }

    /* AI主播信息 - 左侧 */
    .ai-host-info {
        display: flex;
        align-items: center;
        gap: 1.2rem;
        flex: 1;
    }

    .ai-avatar {
        width: 52px;
        height: 52px;
        border-radius: 50%;
        background: linear-gradient(135deg, #64ffda 0%, #48d1cc 100%);
        color: #1a1c1e;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 26px;
        font-weight: 600;
        letter-spacing: -1px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        box-shadow: 0 4px 15px rgba(100,255,218,0.3);
    }

    .ai-details {
        display: flex;
        flex-direction: column;
        gap: 6px;
    }

    .ai-name {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
        letter-spacing: 0.2px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .ai-status {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    /* VIP标签样式 */
    .vip-badge {
        background: linear-gradient(45deg, #ffd700, #ff8c00);
        color: #1a1c1e;
        padding: 3px 10px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 600;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        box-shadow: 0 2px 8px rgba(255,215,0,0.3);
    }

    .expire-date {
        color: rgba(255,255,255,0.7);
        font-size: 12px;
        letter-spacing: 0.5px;
    }

    .computing-power {
        color: #64ffda;
        font-size: 12px;
        padding: 3px 10px;
        background: rgba(100,255,218,0.1);
        border-radius: 6px;
        letter-spacing: 0.5px;
        border: 1px solid rgba(100,255,218,0.2);
    }

    /* 连接线容器 */
    .connection-line {
        flex: 1;
        max-width: 180px;
        height: 80px;  /* 给定固定高度 */
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 连接线 */
    .line {
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #64ffda 0%, #48d1cc 100%);
        transform: translateY(-50%);
        box-shadow: 0 0 20px rgba(100,255,218,0.3);
    }

    /* 麦克风图标容器 */
    .broadcast-icon {
        width: 44px;
        height: 44px;
        background: rgba(22, 27, 34, 0.9);
        border: 2px solid #48d1cc;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 22px;
        color: #64ffda;
        cursor: pointer;
        transition: all 0.3s ease;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 2;
    }

    /* 声音名称/提示 */
    .selected-voice,
    .voice-hint {
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 13px;
        z-index: 1;
    }

    .selected-voice {
        color: #64ffda;
        background: rgba(13, 17, 23, 0.8);
        border: 1px solid rgba(100, 255, 218, 0.2);
    }

    .voice-hint {
        color: rgba(255, 255, 255, 0.5);
        background: rgba(13, 17, 23, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.1);
        font-size: 12px;
    }

    /* 轮班模式文字样式 */
    .rotation-mode-text {
        color: #7c3aed;
        font-weight: 600;
        align-items: center;
        background: rgba(124, 58, 237, 0.15);
        border-radius: 4px;
        padding: 2px 6px;
        font-size: 12px;
        border: 1px solid rgba(124, 58, 237, 0.3);
    }

    /* 高级设置弹窗专门样式 */
    .advanced-settings-modal {
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .advanced-settings-modal .modal-header {
        flex-shrink: 0;
    }

    .advanced-settings-modal .modal-body {
        flex: 1;
        min-height: 0;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 1rem;
        scrollbar-width: thin; /* Firefox */
        scrollbar-color: rgba(100, 255, 218, 0.3) rgba(22, 27, 34, 0.2); /* Firefox */
    }

    /* 高级设置弹窗滚动条样式 */
    .advanced-settings-modal .modal-body::-webkit-scrollbar {
        width: 6px;
    }

    .advanced-settings-modal .modal-body::-webkit-scrollbar-track {
        background: rgba(22, 27, 34, 0.2);
        border-radius: 3px;
    }

    .advanced-settings-modal .modal-body::-webkit-scrollbar-thumb {
        background: rgba(100, 255, 218, 0.3);
        border-radius: 3px;
        transition: all 0.3s ease;
    }

    .advanced-settings-modal .modal-body::-webkit-scrollbar-thumb:hover {
        background: rgba(100, 255, 218, 0.5);
    }

    /* 助播声音选择弹窗专门样式 */
    .assi-voice-modal {
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .assi-voice-modal .modal-header {
        flex-shrink: 0;
    }

    .assi-voice-modal .modal-body {
        flex: 1;
        min-height: 0;
        overflow: hidden;
        padding: 1rem;
    }

    /* 助播声音网格双列布局 */
    .assi-voice-grid {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 12px;
        max-height: 400px;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 0 !important;
        scrollbar-width: thin; /* Firefox */
        scrollbar-color: rgba(100, 255, 218, 0.3) rgba(22, 27, 34, 0.2); /* Firefox */
    }

    /* 助播声音网格滚动条样式 */
    .assi-voice-grid::-webkit-scrollbar {
        width: 6px;
    }

    .assi-voice-grid::-webkit-scrollbar-track {
        background: rgba(22, 27, 34, 0.2);
        border-radius: 3px;
    }

    .assi-voice-grid::-webkit-scrollbar-thumb {
        background: rgba(100, 255, 218, 0.3);
        border-radius: 3px;
        transition: all 0.3s ease;
    }

    .assi-voice-grid::-webkit-scrollbar-thumb:hover {
        background: rgba(100, 255, 218, 0.5);
    }

    /* 抖音账号信息 - 右侧 */
    .douyin-account-info {
        display: flex;
        align-items: center;
        gap: 1.2rem;
        flex: 1;
        justify-content: flex-end;
    }

    .douyin-details {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 6px;
    }

    .douyin-name {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
        letter-spacing: 0.2px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .douyin-avatar {
        width: 52px;
        height: 52px;
        border-radius: 50%;
        overflow: hidden;
        border: 2px solid rgba(100,255,218,0.2);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .douyin-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* 动画效果优化 */
    @keyframes flowLight {
        0% {
            background-position: -200px 0;
            opacity: 0.5;
        }
        50% {
            opacity: 1;
        }
        100% {
            background-position: 200px 0;
            opacity: 0.5;
        }
    }

    .line::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 200%;
        height: 100%;
        background: linear-gradient(90deg, 
            transparent 0%,
            rgba(100,255,218,0.8) 50%,
            transparent 100%
        );
        background-size: 200px 100%;
        animation: flowLight 3s ease-in-out infinite;
    }

    /* 麦克风图标动画 */
    @keyframes pulse {
        0% {
            transform: translate(-50%, -50%) scale(1);
            box-shadow: 0 0 0 0 rgba(100,255,218,0.4);
        }
        70% {
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: 0 0 0 10px rgba(100,255,218,0);
        }
        100% {
            transform: translate(-50%, -50%) scale(1);
            box-shadow: 0 0 0 0 rgba(100,255,218,0);
        }
    }

    .broadcast-icon {
        animation: pulse 2s infinite;
    }

    /* 连接卡片悬浮效果 */
    .broadcast-connection-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .broadcast-connection-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(100,255,218,0.1);
    }

    /* 头像呼吸灯效果 */
    @keyframes breathe {
        0% {
            box-shadow: 0 0 0 0 rgba(100,255,218,0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(100,255,218,0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(100,255,218,0);
        }
    }

    .ai-avatar, .douyin-avatar {
        animation: breathe 3s infinite;
    }

    /* 语音选择弹窗 */
    .voice-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(5px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .voice-modal-content {
        background: linear-gradient(145deg, rgba(22, 27, 34, 0.95), rgba(13, 17, 23, 0.98));
        border: 1px solid rgba(100, 255, 218, 0.2);
        border-radius: 16px;
        padding: 24px;
        width: 98vw;          /* 弹窗宽度适配窄屏 */
        max-width: 98vw;
    }

    .voice-modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .voice-modal-title {
        color: #64ffda;
        font-size: 18px;
        font-weight: 600;
    }

    .voice-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        max-height: 400px;
        overflow-y: auto;
    }

    .voice-item {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(100, 255, 218, 0.1);
        border-radius: 8px;
        padding: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .voice-item:hover {
        background: rgba(100, 255, 218, 0.1);
        border-color: rgba(100, 255, 218, 0.3);
    }

    .voice-item.active {
        background: rgba(100, 255, 218, 0.15);
        border-color: #64ffda;
    }

    .voice-preview {
        margin-top: 8px;
    }

    /* 声音选择提示样式 */
    .voice-status {
        position: absolute;
        bottom: -25px;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        background: rgba(13, 17, 23, 0.8);
        padding: 4px 12px;
        border-radius: 12px;
        border: 1px solid rgba(100, 255, 218, 0.1);
    }

    .voice-status.selected {
        color: #64ffda;
    }

    .voice-status .click-hint {
        color: rgba(255, 255, 255, 0.5);
        font-size: 11px;
    }

    /* 可收缩输入框容器 */
    .collapsible-input {
        background: linear-gradient(145deg, rgba(22, 27, 34, 0.8), rgba(13, 17, 23, 0.9));
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    /* 输入框头部 */
    .input-header {
        padding: 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        user-select: none;
    }

    /* 箭头图标 */
    .collapse-arrow {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64ffda;
        transition: transform 0.3s ease;
    }

    .collapse-arrow.collapsed {
        transform: rotate(-90deg);
    }

    /* 输入框内容区 */
    .input-content {
        transition: all 0.3s ease;
        max-height: 300px;
        overflow: hidden;
    }

    .input-content.collapsed {
        max-height: 0;
        padding: 0 15px;
    }

    /* 输入框 */
    textarea {
        width: 100%;
        background: rgba(13, 17, 23, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        color: #b6c3d5;
        font-size: 12px;
        line-height: 1.8;
        resize: none;
        height: 160px;
        margin-bottom: 15px;
    }

    /* 基础设置面板 */
    .basic-settings {
        background: linear-gradient(145deg, rgba(22, 27, 34, 0.8), rgba(13, 17, 23, 0.9));
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 15px 20px;
    }

    /* 设置项容器 - 调整间距和布局 */
    .settings-container {
        display: flex;
        align-items: center;
        gap: 20px; /* 减小间距 */
        flex-wrap: wrap; /* 允许在需要时换行 */
    }

    /* 设置项组 */
    .setting-item {
        display: flex;
        align-items: center;
        gap: 10px;
        position: relative; /* 确保相对定位 */
    }

    /* 算力设置项容器 */
    .power-setting-item {
        position: relative;
        min-width: auto; /* 移除最小宽度限制 */
        display: flex;
        align-items: center;
    }



    /* 选项组样式调整 */
    .options-group {
        display: flex;
        align-items: center;
        gap: 8px; /* 减小选项之间的间距 */
        background: rgba(255, 255, 255, 0.05);
        padding: 4px 8px;
        border-radius: 6px;
        white-space: nowrap; /* 防止选项换行 */
    }

    /* 设置标签样式调整 */
    .setting-label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
        white-space: nowrap;
        margin-right: 4px; /* 添加右侧间距 */
    }

    /* 输入框样式优化 */
    .local-power-input input {
        width: 100%;
        padding: 8px 12px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: rgba(255, 255, 255, 0.9);
        font-size: 13px;
        outline: none;
        box-sizing: border-box; /* 确保padding不会导致宽度溢出 */
    }

    /* 高级设置按钮 */
    .advanced-btn {
        margin-left: auto;
        padding: 4px 12px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 6px;
        color: rgba(255, 255, 255, 0.7);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .advanced-btn:hover {
        background: rgba(100, 255, 218, 0.1);
        color: #64ffda;
    }

    /* 样式补充 */
    /* 启动按钮容器 */
    .start-button-container {
        display: flex;
        justify-content: center;
        /*margin-top: 20px;*/
    }

    /* 启动按钮样式 */
    .start-button {
        padding: 10px 30px;
        font-size: 16px;
        font-weight: 500;
        color: #1a1c1e;
        background: linear-gradient(45deg, #64ffda, #48d1cc);
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .start-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(100, 255, 218, 0.2);
    }

    .start-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    /* 样式补充 */
    /* 禁用状态样式 */
    .settings-container.disabled {
        opacity: 0.6;
        pointer-events: none;
    }

    .settings-container.disabled .options-group,
    .settings-container.disabled .local-power-input {
        cursor: not-allowed;
    }

    /* 样式补充 */
    /* 声音选择弹窗样式 */
    .voice-modal-content {
        width: 600px;
        max-width: 90vw;
        max-height: 85vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .voice-modal-content .modal-header {
        flex-shrink: 0;
    }

    .voice-modal-content .modal-body {
        flex: 1;
        min-height: 0;
        overflow: hidden;
        padding: 1rem;
    }

    .voice-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        max-height: 400px;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .voice-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(100, 255, 218, 0.1);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .voice-item:hover {
        background: rgba(100, 255, 218, 0.1);
        border-color: rgba(100, 255, 218, 0.3);
    }

    .voice-item.active {
        background: rgba(100, 255, 218, 0.15);
        border-color: #64ffda;
    }

    .voice-item.selected-multiple {
        background: rgba(124, 58, 237, 0.15);
        border-color: #7c3aed;
    }

    .voice-name {
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        flex: 1;
        margin-left: 8px;
    }

    .voice-checkbox {
        display: flex;
        align-items: center;
        margin-right: 8px;
    }

    .voice-checkbox input[type="checkbox"] {
        width: 16px;
        height: 16px;
        accent-color: #7c3aed;
        cursor: pointer;
    }

    .preview-btn {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: transparent;
        border: 1px solid rgba(100, 255, 218, 0.3);
        border-radius: 50%;
        color: #64ffda;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .preview-btn:hover {
        background: rgba(100, 255, 218, 0.1);
    }

    .preview-btn.playing {
        background: #64ffda;
        color: #1a1c1e;
    }

            /* 轮班模式切换开关 */
    .advanced-mode-toggle {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-left: auto;
    }

    .toggle-label {
        font-size: 14px;
        color: var(--text-secondary);
        font-weight: 500;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.1);
        transition: .3s;
        border-radius: 24px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 2px;
        background-color: rgba(255, 255, 255, 0.7);
        transition: .3s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #7c3aed;
        border-color: #7c3aed;
    }

    input:checked + .slider:before {
        transform: translateX(24px);
        background-color: white;
    }

    /* 高级控制面板 */
    .advanced-controls {
        background: rgba(124, 58, 237, 0.1);
        border: 1px solid rgba(124, 58, 237, 0.2);
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 15px;
        flex-shrink: 0;
    }

    .control-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .voice-interval-setting {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .setting-label {
        font-size: 14px;
        color: #aaa;
        font-weight: 500;
        white-space: nowrap;
    }

    .interval-input-group {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .interval-input {
        width: 60px;
        padding: 6px 8px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        color: var(--text-primary);
        font-size: 14px;
        text-align: center;
    }

    .interval-input:focus {
        outline: none;
        border-color: #7c3aed;
        background: rgba(255, 255, 255, 0.15);
    }

    .interval-unit {
        font-size: 14px;
        color: var(--text-secondary);
    }

    .selected-count {
        font-size: 14px;
        color: #aaa;
        font-weight: 600;
        background: rgba(124, 58, 237, 0.2);
        padding: 4px 12px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;
    }

    /* 紧凑的清空按钮样式 */
    .clear-btn {
        background: rgba(255, 82, 82, 0.15);
        border: 1px solid rgba(255, 82, 82, 0.4);
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ff5252;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 10px;
        flex-shrink: 0;
    }

    .clear-btn:hover:not(:disabled) {
        background: rgba(255, 82, 82, 0.3);
        border-color: #ff5252;
        transform: scale(1.15);
    }

    .clear-btn:disabled {
        opacity: 0.3;
        cursor: not-allowed;
        transform: none;
    }

    .bulk-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        margin-bottom: 10px;
        justify-content: flex-end;
    }

    .bulk-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 12px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.05);
        color: var(--text-primary);
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 13px;
        font-weight: 500;
    }

    .bulk-btn:hover:not(:disabled) {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
    }

    .bulk-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .btn-select-all-personal {
        border-color: rgba(100, 255, 218, 0.3);
        color: #64ffda;
    }

    .btn-select-all-personal:hover:not(:disabled) {
        background: rgba(100, 255, 218, 0.1);
        border-color: #64ffda;
    }

    .btn-select-all-public {
        border-color: rgba(255, 193, 7, 0.3);
        color: #ffc107;
    }

    .btn-select-all-public:hover:not(:disabled) {
        background: rgba(255, 193, 7, 0.1);
        border-color: #ffc107;
    }

    .btn-clear-all {
        border-color: rgba(255, 82, 82, 0.3);
        color: #ff5252;
    }

    .btn-clear-all:hover:not(:disabled) {
        background: rgba(255, 82, 82, 0.1);
        border-color: #ff5252;
    }

    /* 声音计数显示 */
    .voice-count {
        font-size: 12px;
        color: var(--text-secondary);
        margin-left: 8px;
        background: rgba(255, 255, 255, 0.05);
        padding: 2px 6px;
        border-radius: 10px;
    }



    /* 分隔线样式增强 */
    .voice-divider {
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-secondary);
        font-size: 14px;
        font-weight: 600;
        margin: 15px 0 10px 0;
        gap: 8px; /* 添加元素间距 */
    }

    /* 样式补充 */
    /* 助播声音选择按钮 */
    .assi-voice-btn {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 8px 12px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 6px;
        color: rgba(255, 255, 255, 0.9);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .assi-voice-btn:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(100, 255, 218, 0.3);
    }

    .assi-voice-btn i {
        color: #64ffda;
        font-size: 14px;
    }

    /* 样式补充 */
    /* 隐藏滚动条但保持滚动功能 */
    .voice-grid {
        max-height: 500px;
        overflow-y: auto;
        scrollbar-width: thin; /* Firefox - 显示滚动条 */
        scrollbar-color: rgba(100, 255, 218, 0.3) rgba(22, 27, 34, 0.2); /* Firefox */
    }

    /* Webkit浏览器显示滚动条 */
    .voice-grid::-webkit-scrollbar {
        width: 6px;
    }

    .voice-grid::-webkit-scrollbar-track {
        background: rgba(22, 27, 34, 0.2);
        border-radius: 3px;
    }

    .voice-grid::-webkit-scrollbar-thumb {
        background: rgba(100, 255, 218, 0.3);
        border-radius: 3px;
        transition: all 0.3s ease;
    }

    .voice-grid::-webkit-scrollbar-thumb:hover {
        background: rgba(100, 255, 218, 0.5);
    }



    /* 调整弹窗内容区域的样式 */
    .modal-body {
        padding: 1rem;
        overflow-y: auto;
        flex: 1;
        min-height: 0;
    }

    /* 样式补充 */
    /* 字数统计样式 */
    .word-count {
        position: absolute;
        right: 15px;
        bottom: 15px;
        background: rgba(13, 17, 23, 0.8);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
        line-height: 1;
        backdrop-filter: blur(4px);
    }

    .word-count.warning {
        color: #ff9800;
        background: rgba(255, 152, 0, 0.1);
    }

    .word-count.error {
        color: #ff5252;
        background: rgba(255, 82, 82, 0.1);
    }

    /* 输入框容器样式调整 */
    .input-content {
        position: relative;
    }

    /* 调整textarea的padding */
    textarea {
        padding: 15px;
        padding-right: 85px; /* 为字数统计留出空间 */
        min-height: 120px;
    }

    /* 样式补充 */
    /* 当前播放内容显示 */
    .current-playing {
    position: fixed;
    left: 50%;
    bottom: 40px;
    transform: translateX(-50%);
    max-width: 80%;
    background: linear-gradient(145deg, rgba(13, 17, 23, 0.95), rgba(22, 27, 34, 0.95));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(100, 255, 218, 0.2);
    border-radius: 12px;
    padding: 20px 30px;
    color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    opacity: 0;
    transition: all 0.3s ease;
}

/* 系统消息样式 */
.current-playing.system-message {
    background: linear-gradient(145deg, rgba(22, 22, 35, 0.95), rgba(28, 28, 45, 0.95));
    border: 1px solid rgba(255, 224, 130, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.current-playing.system-message .playing-icon {
    color: #ffb300;
}

.current-playing.system-message .current-text {
    color: rgba(255, 224, 130, 0.9);
    font-style: italic;
}

    .current-playing.active {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }

    .current-playing.fade-out {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }

    /* 播放图标动画 */
    .playing-icon {
    display: inline-flex;
    align-items: center;
    margin-right: 12px;
    color: #64ffda;
    position: relative;
}

.control-hint {
    font-size: 12px;
    margin-left: 5px;
    background: rgba(100, 255, 218, 0.15);
    border-radius: 4px;
    padding: 2px 6px;
    color: #64ffda;
    transition: all 0.3s ease;
    position: relative;
}

.playing-header {
    display: flex;
    align-items: center;
    width: 100%;
}





/* 提示文字闪烁动画 */
.hint-flash {
    animation: flash-animation 1s ease;
    background: rgba(100, 255, 218, 0.5);
}

@keyframes flash-animation {
    0% { background: rgba(100, 255, 218, 0.15); }
    50% { background: rgba(100, 255, 218, 0.5); }
    100% { background: rgba(100, 255, 218, 0.15); }
}

    /* 当前文本样式 */
    .current-text {
        font-size: 16px;
        line-height: 1.6;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    /* 添加播放控件样式 */
    .play-controls {
        display: flex;
        align-items: center;
        margin-top: 12px;
        padding-top: 8px;
        border-top: 1px solid rgba(100, 255, 218, 0.1);
    }

    .progress-bar {
        flex: 1;
        height: 4px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        overflow: hidden;
        margin-right: 10px;
    }

    .progress-inner {
        height: 100%;
        background: #64ffda;
        border-radius: 2px;
        transition: width 0.1s linear;
    }

    .time-display {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        min-width: 70px;
        text-align: right;
    }

    /* 样式补充 */
    /* 特殊交待输入框样式 */
    .special-input textarea {
        min-height: 80px; /* 减小最小高度 */
        height: 80px; /* 固定高度 */
    }

    /* 样式补充 */
    /* 提示信息样式 */
    .tooltip {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: rgba(0, 0, 0, 0.8);
        color: #fff;
        padding: 10px 20px;
        border-radius: 5px;
        font-size: 14px;
        text-align: center;
        z-index: 1001;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
    }

    .tooltip.show {
        opacity: 1;
    }

    /* 样式补充 */
    /* 提示信息类型样式 */
    .tooltip-info {
        border: 2px solid #64ffda;
    }

    .tooltip-success {
        border: 2px solid #238636;
    }

    .tooltip-warning {
        border: 2px solid #ff9800;
    }

    .tooltip-error {
        border: 2px solid #ff5252;
    }

    /* Tooltip 样式优化 */
    .tooltip {
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(145deg, rgba(13, 17, 23, 0.95), rgba(22, 27, 34, 0.95));
        color: #fff;
        padding: 12px 30px;
        border-radius: 12px;
        font-size: 14px;
        text-align: center;
        z-index: 2000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        min-width: 200px;
        transform: translate(-50%, -20px);
        border: 1px solid rgba(255, 152, 0, 0.2);
        box-shadow: 
            0 8px 24px rgba(255, 152, 0, 0.15),
            inset 0 0 0 1px rgba(255, 152, 0, 0.1);
    }

    /* 不同类型的 tooltip 样式 */
    .tooltip-info {
        border-left: 4px solid #ffa500;
        box-shadow: 
            0 8px 24px rgba(255, 165, 0, 0.15),
            inset 0 0 0 1px rgba(255, 165, 0, 0.1);
    }

    .tooltip-success {
        border-left: 4px solid #2ecc71;
        box-shadow: 
            0 8px 24px rgba(46, 204, 113, 0.15),
            inset 0 0 0 1px rgba(46, 204, 113, 0.1);
    }

    .tooltip-warning {
        border-left: 4px solid #ff9800;
        box-shadow: 
            0 8px 24px rgba(255, 152, 0, 0.15),
            inset 0 0 0 1px rgba(255, 152, 0, 0.1);
    }

    .tooltip-error {
        border-left: 4px solid #ff5252;
        box-shadow: 
            0 8px 24px rgba(255, 82, 82, 0.15),
            inset 0 0 0 1px rgba(255, 82, 82, 0.1);
    }

    .tooltip.show {
            opacity: 1;
        visibility: visible;
            transform: translate(-50%, 0);
        animation: 
            tooltipSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards,
            tooltipGlow 2s ease-in-out infinite;
    }

    /* 添加发光动画 */
    @keyframes tooltipGlow {
        0% {
            box-shadow: 
                0 8px 24px rgba(255, 165, 0, 0.15),
                inset 0 0 0 1px rgba(255, 165, 0, 0.1);
        }
        50% {
            box-shadow: 
                0 8px 32px rgba(255, 165, 0, 0.25),
                inset 0 0 0 1px rgba(255, 165, 0, 0.2);
        }
        100% {
            box-shadow: 
                0 8px 24px rgba(255, 165, 0, 0.15),
                inset 0 0 0 1px rgba(255, 165, 0, 0.1);
        }
    }

    /* 算力显示和设置样式 */
    .computing-power {
        position: relative;
        display: inline-flex;
        align-items: center;
    }

    .power-settings-trigger {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 12px;
        border-radius: 6px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .power-settings-trigger:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 152, 0, 0.2);
    }

    /* 算力设置弹窗 */
    .power-settings-popup {
        position: absolute;
        top: calc(100% + 8px);
        right: 0;
        width: 300px;
        background: linear-gradient(145deg, rgba(22, 27, 34, 0.98), rgba(13, 17, 23, 0.98));
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        z-index: 1001; /* 确保在其他元素之上 */
        backdrop-filter: blur(10px);
    }

    /* 算力选项样式 */
    .power-option {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 12px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        margin-bottom: 8px;
    }

    .power-option:hover {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 152, 0, 0.2);
    }

    .power-option.active {
        background: rgba(255, 152, 0, 0.1);
        border-color: #ffa500;
    }

    .power-option-icon {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        background: rgba(255, 152, 0, 0.1);
        color: #ffa500;
        flex-shrink: 0;
    }

    .power-option-info {
        flex: 1;
    }

    .power-option-title {
        font-size: 14px;
        font-weight: 500;
        color: #fff;
        margin-bottom: 4px;
    }

    .power-option-desc {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
    }

    .power-option-desc .local-power-input {
        margin-top: 8px;
        width: 100%;
    }

    .power-option-desc .local-power-input input {
        width: 100%;
        padding: 8px 12px;
        background: rgba(13, 17, 23, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 6px;
        color: #fff;
        font-size: 13px;
        outline: none;
    }

    .power-option-desc .local-power-input input:focus {
        border-color: #ffa500;
    }

    .power-balance {
        margin-top: 4px;
        color: #ffa500;
    }

    /* 本地算力输入框 */

    .local-power-input input {
        width: 100%;
        padding: 8px 12px;
        background: rgba(13, 17, 23, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 6px;
        color: #fff;
        font-size: 13px;
        outline: none;
    }

    /* 充值按钮 */
    .recharge-button {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 6px;
        background: linear-gradient(45deg, #ffd700, #ffa500);
        color: #000;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .recharge-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(255, 165, 0, 0.2);
    }

    /* 箭头动画 */
    .fa-chevron-down {
        transition: transform 0.3s ease;
    }

    .fa-chevron-down.rotate {
        transform: rotate(180deg);
    }

	 .modal-title {
		 display: flex;
		 align-items: center;
		 gap: 15px;
	 }
	 
	 .clone-btn {
		 display: inline-flex;
		 align-items: center;
		 gap: 6px;
		 padding: 6px 12px;
		 background: linear-gradient(45deg, #64ffda, #48cca8);
		 border-radius: 6px;
		 color: #1a1c1e;
		 font-size: 14px;
		 text-decoration: none;
		 transition: all 0.3s ease;
	 }
	 
	 .clone-btn:hover {
		 transform: translateY(-1px);
		 box-shadow: 0 4px 12px rgba(100, 255, 218, 0.2);
	 }
	 
	 .clone-btn i {
		 font-size: 14px;
	 }
    /* 添加移动端响应式样式 */
    @media screen and (max-width: 768px) {
        /* 整体布局调整 */
        .main-container {
            padding: 10px;
        }

        /* 输入区域调整 */
        .collapsible-input {
            margin-bottom: 10px;
        }

        .input-header {
            padding: 10px;
            font-size: 14px;
        }

        textarea {
            min-height: 100px;
        }

        /* 控制面板调整 */
        .control-panel {
            margin-bottom: 120px; /* 增加底部间距,避免被底部工具栏遮挡 */
        }

        /* 设置项调整 */
        .setting-item {
            padding: 10px;
            margin-bottom: 10px;
        }

        .options-group {
            flex-wrap: wrap;
            gap: 10px;
        }

        /* 算力设置弹窗调整 */
        .power-settings-popup {
            width: 90%;
            max-width: 300px;
            right: auto;
            left: 50%;
            transform: translateX(-50%);
        }

        .power-option {
            padding: 10px;
        }

        .power-option-icon {
            width: 28px;
            height: 28px;
        }

        /* 底部工具栏调整 */
        .chat-tools {
            padding: 10px;
        }

        .chat-buttons {
            flex-wrap: wrap;
        }

        .chat-buttons .btn-secondary {
            padding: 8px 12px;
            font-size: 14px;
        }

        /* 会话指示器位置调整 */
        .session-indicator {
            bottom: 100px;
            right: 10px;
        }

        /* 语音选择区域调整 */
        .voice-selection-area {
            flex-wrap: wrap;
            gap: 8px;
        }

        /* 高级设置按钮调整 */
        .advanced-btn {
            width: 100%;
            margin: 10px 0;
        }

        /* 启动按钮调整 */
        .start-button {
            width: 100%;
            margin: 10px 0;
            padding: 12px;
        }
    }

    /* 优化移动端点击区域 */
    @media (hover: none) and (pointer: coarse) {
        .power-settings-trigger,
        .power-option,
        .input-header,
        button {
            min-height: 44px; /* 确保触摸区域足够大 */
        }

        input[type="radio"],
        input[type="checkbox"] {
            width: 20px;
            height: 20px;
        }
    }

    /* 移动端样式优化 */
    @media screen and (max-width: 768px) {
        /* 基础容器宽度设置 */
        .app-container {
            width: 700px !important;
            min-width: 700px !important;
            margin: 0 auto;
            padding: 10px;
            box-sizing: border-box;
            overflow-x: auto;
        }

        /* 主要内容区域 */
        .main-content {
            width: 700px !important;
            margin: 0 auto;
        }

        /* 广播连接卡片 */
        .broadcast-connection-card {
            width: 100%;
            margin: 10px 0;
            padding: 15px;
        }

        /* 头部布局调整 */
        .host-header {
            width: 100%;
            padding: 10px;
        }

        /* AI主播信息区域 */
        .ai-host-info {
            width: 100%;
        }

        /* 算力设置弹窗 */
        .power-settings-popup {
            width: 300px;
            right: 0;
            left: auto;
            transform: none;
        }

        /* 输入区域调整 */
        .input-section {
            width: 100%;
            grid-template-columns: 1fr;
        }

        /* 控制面板调整 */
        .control-panel {
            width: 100%;
            margin-bottom: 80px;
        }

        /* 底部工具栏 */
        .chat-tools {
            width: 700px;
            left: 50%;
            transform: translateX(-50%);
        }
    }

    /* 在现有样式基础上添加 */
    .trial-info {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
    }

    .trial-actions {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .trial-badge {
        background: #4CAF50;
        color: white;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
    }

    .expire-date {
        color: #666;
        font-size: 12px;
    }

    .daily-usage {
        color: #FF9800;
        font-size: 12px;
    }

    .claim-trial-btn {
        background: #2196F3;
        color: white;
        padding: 4px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 13px;
        margin-right: 12px;
    }

    .activate-btn {
        background: #FF5722;
        color: white;
        padding: 4px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 13px;
    }

    .normal-badge {
        background: #FF5722;
        color: white;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
    }

    /* 添加悬停效果 */
    .claim-trial-btn:hover,
    .activate-btn:hover,
    .normal-badge:hover {
        opacity: 0.9;
    }

    /* Toast 提示 */
    .toast {
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        padding: 10px 20px;
        border-radius: 4px;
        color: white;
        z-index: 9999;
        animation: fadeIn 0.3s;
    }

    .toast.success {
        background-color: #4CAF50;
    }

    .toast.error {
        background-color: #F44336;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translate(-50%, -20px); }
        to { opacity: 1; transform: translate(-50%, 0); }
    }

    .toast {
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        padding: 10px 20px;
        border-radius: 4px;
        color: white;
        z-index: 9999;
        animation: fadeIn 0.3s;
    }
    
    .toast.success {
        background-color: #4CAF50;
    }
    
    .toast.error {
        background-color: #F44336;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translate(-50%, -20px); }
        to { opacity: 1; transform: translate(-50%, 0); }
    }

    .tutorial-link:hover {
        background: var(--accent) !important;
        color: white !important;
    }
    .code-block {
        position: relative;
        transition: all 0.3s ease;
    }
    .code-block:hover {
        background: rgba(0,0,0,0.3);
    }
    .modal-content {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .code-block::-webkit-scrollbar {
        width: 8px;
    }
    .code-block::-webkit-scrollbar-track {
        background: rgba(0,0,0,0.1);
        border-radius: 4px;
    }
    .code-block::-webkit-scrollbar-thumb {
        background: var(--text-secondary);
        border-radius: 4px;
    }
    .code-block::-webkit-scrollbar-thumb:hover {
        background: var(--accent);
    }

    /* 步骤样式 */
    .steps-container {
        padding: 20px 0;
    }

    .modal-title {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .modal-title h3 {
        margin: 0;
    }

    /* 添加section-header样式 */
    .section-header {
        grid-column: 1 / -1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
    }

    /* 修改customize-voice-btn样式 */
    .customize-voice-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 4px 12px;
        background: linear-gradient(45deg, #64ffda, #48cca8);
        border-radius: 6px;
        color: #1a1c1e;
        font-size: 12px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(100, 255, 218, 0.2);
    }

    /* 修改section-title样式 */
    .section-title {
        color: var(--text-primary);
        font-size: 16px;
        margin: 0;
        padding: 0;
        font-weight: 500;
    }

    .customize-voice-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(100, 255, 218, 0.3);
    }

    .customize-voice-btn i {
        font-size: 14px;
    }

    .step {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    /* 添加新样式 */
    .voice-section {
        margin-bottom: 20px;
    }

    /* 修改分隔线样式 */
    .voice-divider {
        position: relative;
        text-align: center;
        margin: 32px -20px;  /* 负边距延伸到容器边缘 */
        padding: 0 20px;     /* 内边距补偿负边距 */
        border-bottom: 1px solid var(--border);
    }

    .voice-divider span {
        position: relative;
        display: inline-block;
        top: 10px;
        background: var(--bg-card);
        padding: 0 20px;
        color: var(--text-secondary);
        font-size: 14px;
        font-weight: 500;
    }

    /* 调整语音网格布局 */
    .voice-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 12px;
        padding: 0 20px;
    }

    /* 调整语音项样式 */
    .voice-item {
        background: rgba(13, 17, 23, 0.3);
        border: 1px solid var(--border);
        border-radius: 8px;
        padding: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.2s;
    }

    .voice-item:hover {
        background: rgba(13, 17, 23, 0.5);
    }

    /* 分隔线容器样式 */
    .divider-container {
        grid-column: 1 / -1;
        margin: 20px 0;
        padding: 0;
        display: flex;
        flex-direction: column;  /* 改为纵向排列 */
        justify-content: center;
        align-items: center;
        position: relative;
    }

    /* 修改分隔线样式 */
    .voice-divider {
        position: relative;
        width: 100%;
        text-align: center;
        margin: 0;
        padding: 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px; /* 添加元素间距 */
    }

    /* 分隔线文字样式 */
    .voice-divider span {
        position: relative;
        display: inline-block;
        top: 10px;
        background: var(--bg-card);
        padding: 0 20px;
        color: var(--text-secondary);
        font-size: 14px;
        font-weight: 500;
    }

    /* 声音计数样式 */
    .voice-divider .voice-count {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 2px 8px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
        font-weight: 500;
        top: 10px;
    }

    /* 分隔线按钮样式 */
    .divider-btn {
        background: rgba(100, 255, 218, 0.1);
        border: 1px solid rgba(100, 255, 218, 0.3);
        border-radius: 16px;
        padding: 4px 12px;
        font-size: 12px;
        color: #64ffda;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: 500;
        top: 10px;
        position: relative;
    }

    .divider-btn:hover:not(:disabled) {
        background: rgba(100, 255, 218, 0.2);
        border-color: #64ffda;
        transform: translateY(-1px);
    }

    .divider-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    .divider-btn i {
        font-size: 10px;
    }

    /* 定制声音按钮在分隔线容器中的样式 */
    .divider-container .customize-voice-btn {
        position: absolute;
        right: 0;
        top: -10px;
        background: var(--bg-card);
        padding: 4px 12px;
    }

    /* 添加声音按钮样式 */
    .add-voice-btn {
        text-decoration: none;
        border: 1px dashed rgba(255, 152, 0, 0.3) !important;
        transition: all 0.3s ease;
        background: rgba(255, 152, 0, 0.05) !important;
    }

    .add-voice-btn:hover {
        background: rgba(255, 152, 0, 0.1) !important;
        border-color: rgba(255, 152, 0, 0.5) !important;
    }

    .add-voice-content {
        display: flex;
        align-items: center;
        gap: 10px;
        color: #ff9800;
        width: 100%;
        justify-content: center;
    }

    .add-voice-content i {
        font-size: 20px;
        color: #ff9800;
    }

    .add-voice-text {
        font-size: 14px;
        color: #ff9800;
    }

    /* 为不支持:has选择器的浏览器提供备选方案 */
    input[type="radio"]:checked:disabled + span,
    input[type="radio"]:checked:disabled ~ span {
        color: var(--accent);
        font-weight: bold;
    }

    /* 添加禁用状态下复选框的样式 */
    input[type="checkbox"]:disabled {
        opacity: 0.6;
    }
    
    /* 确保禁用状态下选中的复选框更明显 */
    input[type="checkbox"]:checked:disabled {
        opacity: 0.8;
        box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.5);
    }
    
    /* 为复选框的父元素添加样式，使选中项更明显 */
    label:has(input[type="checkbox"]:checked:disabled) {
        color: var(--accent);
        font-weight: bold;
    }
    
    /* 为不支持:has选择器的浏览器提供备选方案 */
    input[type="checkbox"]:checked:disabled + span,
    input[type="checkbox"]:checked:disabled ~ span {
        color: var(--accent);
        font-weight: bold;
    }

    /* 值显示 */
    /* 智能发挥选项分组样式 */
    .option-group-container {
        display: flex;
        align-items: center;
        background: var(--bg-secondary);
        border-radius: 8px;
        padding: 4px;
    }
    
    .ai-option-group {
        display: flex;
        background: var(--bg-tertiary);
        border-radius: 6px;
        padding: 4px 8px;
    }
    
    .reception-option-group {
        /*background: var(--accent-tertiary);*/
        border-radius: 6px;
        padding: 4px 8px;
    }
    
    .option-divider {
        width: 2px;
        height: 24px;
        background: var(--border);
        margin: 0 8px;
    }

    /* 移动端优化：在窄屏设备上也保持两列布局 */
    @media (max-width: 480px) {
        .voice-modal-content {
            width: 100vw !important;   /* 撑满屏幕宽度 */
            max-width: 100vw !important;
            border-radius: 0;          /* 去除圆角，贴合边缘 */
            padding: 16px;            /* 适当内边距 */
        }
        .voice-grid {
            grid-template-columns: repeat(2, 1fr) !important; /* 强制两列 */
            padding: 0 10px;      /* 减少左右留白 */
        }
        .voice-item {
            padding: 10px;        /* 卡片内部留白适配 */
        }
    }
    </style>
<script src="/static/vue.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div id="app" class="app-container"><!----> <!----> <div class="broadcast-connection-card"><div class="ai-host-info"><div class="ai-avatar">
                    A
                </div> <div class="ai-details"><div class="ai-name">a64053493</div> <div class="ai-status"><span class="claim-trial-btn">领取3天体验</span> <span class="activate-btn">激活会员</span> <div class="computing-power"><div class="power-settings-trigger"><i class="fas fa-microchip" style="color: rgb(255, 165, 0);"></i> <span>云算力: 0.0h</span> <i class="fas fa-chevron-down" style="font-size: 12px; margin-left: 4px; color: rgba(255, 255, 255, 0.5);"></i></div> <div class="power-settings-popup" style="display: none;"><div class="power-option active"><div class="power-option-icon"><i class="fas fa-cloud"></i></div> <div class="power-option-info"><div class="power-option-title">云端算力</div> <div class="power-option-desc"><div>使用云端服务器进行推理</div> <div class="power-balance">剩余: 0.0小时</div></div></div> <div class="recharge-button"><i class="fas fa-plus"></i>
                                        充值
                                    </div></div> <div class="power-option"><div class="power-option-icon"><i class="fas fa-server"></i></div> <div class="power-option-info"><div class="power-option-title">本地算力</div> <div class="power-option-desc"><div>使用本地服务器进行推理</div> <div class="local-power-input"><input type="text" placeholder="填写主机地址http://开头"></div></div></div></div></div></div></div></div></div> <div class="connection-line"><div class="line"></div> <div class="voice-selection-area"><div class="broadcast-icon">🎙️</div> <div class="voice-hint" style="cursor: pointer;">
                        点击选择声音<i class="fas fa-chevron-down" style="font-size: 12px; margin-left: 4px; color: rgba(255, 255, 255, 0.5);"></i></div></div></div> <div class="douyin-account-info"><div class="douyin-details"><div class="douyin-name">嘀嗒嘀嗒嘀嗒</div> <div style="font-size: 14px; color: var(--text-secondary); margin-top: 4px; cursor: pointer; display: flex; align-items: center;"><i class="fas fa-link" style="margin-right: 4px;"></i>
                        连接公屏
                    </div></div> <div class="douyin-avatar"><img src="https://p26.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-i-0813_ooCX9AnfpFnb9A52MATyD2NDAe48IAlGAQgA0B.jpeg?from=**********" alt="账号头像"></div></div></div> <div class="main-content" style="margin-bottom: 100px;"><div class="input-section"><div class="collapsible-input"><div class="input-header"><span>1. 参考话术（请告诉我要直播的内容，我会按照您的指示进行直播）</span> <span class="collapse-arrow">▼</span></div> <div class="input-content"><textarea placeholder="请输入主播话术内容..."></textarea> <div class="word-count">
                            2/999999                        </div></div></div> <div class="collapsible-input"><div class="input-header"><span>2. 特殊交待（风格、语气、注意事项、信息补充等）</span> <span class="collapse-arrow collapsed">▼</span></div> <div class="input-content special-input collapsed"><textarea placeholder="有什么特别要求吗？"></textarea> <div class="word-count">
                            63/500
                        </div></div></div></div> <div class="control-panel"><div class="basic-settings"><div class="settings-container"><div class="setting-item"><span class="setting-label">智能发挥</span> <div class="options-group"><div class="option-group-container"><div class="ai-option-group"><label><input type="radio" value="1"><span>全自由</span></label> <label><input type="radio" value="2"><span>半自由</span></label> <label><input type="radio" value="0"><span>关闭</span></label></div> <div class="reception-option-group"><label><input type="radio" value="3"><span>纯接待</span></label></div></div></div></div> <div class="setting-item"><span class="setting-label">用户接待</span> <div class="options-group"><label><input type="checkbox"><span>欢迎</span></label> <label><input type="checkbox"><span>回复</span></label></div></div> <button class="advanced-btn">
                            高级设置
                        </button></div></div> <div class="start-button-container"><button disabled="disabled" class="start-button"><span>请先激活会员</span></button></div> <!----></div></div> <div class="chat-tools"><div class="chat-input"><input type="text" placeholder="输入插话内容，回车发送" class="input-styled"></div> <div class="chat-buttons"><button class="btn-secondary">
                    插话
                </button> <button class="btn-secondary">
                    模拟提问
                </button></div></div> <div class="session-indicator"><span class="session-count">0</span></div> <!----> <!----> <!----> <!----> <!----></div>

    <script src="/static/weui_res/jquery.min.js"></script>
    <script src="/static/weui_res/jquery-weui.min.js"></script>
    <script src="/static/vue/axios.min.js"></script>
    <script>
    //弄三个队， 主线话术/互动话术/插入话术，优先级： 插入话术>互动话术>主话术
      var app = new Vue({
        el: '#app',
        data: {
          entire_text: '--',
          extract_info:'',
          info_text:"产品：ai直播系统\n价格：29800\n功能：替代真人主播讲话\n卖点：够智能，所以用起来很简单，啥都不用配\n\n请多主动跟观众互动",
          rewrite_entire_text:'',
          isReading:false,
          sentences: [] ,
          sentences_prepare: [] ,
          audioQueue:{reply:[],welcome:[],insert:[],main:[]} ,
          audioCache1: [], // 存储池，保存最新的100条主线话术
          audioCache2: [], // 播放池，用于实际播放
          buttonTxt:'启动ai主播',
          buttonColor:'',
          currentT:'',
          currentStopTime:0,//当前句子停顿
          longStopMode: false, // 长停顿模式标志
          voice:'',
          temperature:'',
          speed:'',
          prompt:'',
          config:{},
          running:false,
          open_id:'3b07741e-82a9-50de-9991-7d26674ede46',
          room_id:'',
          platform:'douyin',
          live_messages:{comment:[],room_enter:[],room_follow:[],room_like:[]},
          message_cursor:'', // 数据库时间戳游标
          douyin_cursor: '', // 抖音接口游标
          insertText:'',
          tooltip:'',
          tooltipShow:false,
          welcomeFlag:false,
          replyFlag:true,
          ignore_users:"嘀嗒嘀嗒嘀嗒",
          ai_live_right:'0',
          heartbeat: {},
          rewriteMode: 1,
          audioList: {},
          selectedRefAudio:'',
        selectedRefAssiAudio:'',
          last_welcome_time:0,
          current_wavurl:'',
          self_tts_host:'',
          showPowerSettings: false,
          computilityType:0, //0-云算力 1-本地算力
          sessionToken: '',
          activeSessions: [], // 正在运行的会话列表
          sessionListVisible: false, // 会话列表是否可见
        vip_expired:0, //会员到期时间
        ccp_remain:0, //云算力余额
        ba:[],
          showAdvancedSettings: false,  // 控制高级设置的显示与藏
          replyTemplate: "{评论内容}，{用户昵称}，呃，{回复}",  // 公屏回复模板的默认值
          forbiddenWords: '', // 自定义违规词
          showVoiceModal: false,
          isCollapsed: {
              reference: true,
              special: true
          },
          currentPlayingVoice: '', // 当前正在播放的声音
          audioPlayer: null,
          showAssiVoiceModal: false, // 助播声音选择弹窗显示状态
                     // 轮班声音模式相关
           advancedVoiceMode: false, // 轮班模式开关
           selectedVoicesMultiple: [], // 多选声音列表（MD5）
           selectedVoiceIds: [], // 多选声音ID列表
           voiceChangeInterval: 5, // 声音切换间隔（分钟）
          voiceChangeTimer: null, // 声音切换定时器
          currentVoiceIndex: 0, // 当前使用的声音索引
          showCurrentText: true,
          canClaimTrial: true,
          in_trial: false,
          trial_expired: 0,
          trial_daily_usage: 0,
          trialDaysLeft: 0,
          trialTimeLeft: 0,
          toast: {
              show: false,
              message: '',
              type: 'success'
          },
          requestRetryCount: 0,  // 添加重试计数
          showRoomIdModal: false,
          inputRoomId: '',
          jsCode: '',
          web_id: '',
          msggetType: 0, // 消息获取类型：0-未确定，1-cookie获取，2-辅助插件推送
          messageIntervalId: null, // 消息获取定时器ID
          playNextTimer: null, // 添加定时器指针
          audioContext: null, // Web Audio API AudioContext
          currentSourceNode: null, // 当前播放的 AudioBufferSourceNode
          previewAudioContext: null, // 用于预览的 AudioContext
          previewSourceNode: null, // 用于预览的 AudioBufferSourceNode
          isPaused: false, // 播放暂停状态
          playProgress: 0, // 播放进度百分比
          playStartTime: 0, // 播放开始时间
          currentAudioDuration: 0, // 当前音频总时长
          progressTimer: null, // 进度更新定时器
          currentBuffer: null, // 当前播放的音频数据
          isSystemMessage: false, // 是否为系统消息（非TTS内容）
          isDraggingProgress: false, // 新增：标记是否正在拖动进度条
          // 超级克隆相关变量
          superCloneIndex: 0, // 超级克隆子声音轮换索引
          superCloneSubVoices: [], // 当前选中声音的子声音列表
          superCloneIndexMap: {}, // 记录每个超级声音的子声音索引
          superCloneSubVoicesMap: {}, // 记录每个超级声音的子声音列表
        },
        mounted: function () {
            // 获取或生成web_id
            this.web_id = window.localStorage.getItem('web_id');
            if (!this.web_id) {
                this.web_id = 'web_' + 'a64053493' + '_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9) ;
                window.localStorage.setItem('web_id', this.web_id);
            }
            ///this.bap();
            window.addEventListener('beforeunload', this.handlePageRefreshOrClose);
            this.config = window.localStorage.getItem("config")? JSON.parse(window.localStorage.getItem("config")) : {};
            if(this.config.entire_text) this.entire_text = this.config.entire_text;
        if(this.config.extract_info) this.extract_info = this.config.extract_info;
            if(this.config.info_text) this.info_text = this.config.info_text;
                    if(this.config.selectedRefAudio) this.selectedRefAudio = this.config.selectedRefAudio;
        if(this.config.selectedRefAssiAudio) this.selectedRefAssiAudio = this.config.selectedRefAssiAudio;
            if(this.config.self_tts_host) this.self_tts_host = this.config.self_tts_host;
            if(this.config.computilityType) this.computilityType = this.config.computilityType;
            // 加载轮班声音模式配置
            if(this.config.advancedVoiceMode) this.advancedVoiceMode = this.config.advancedVoiceMode;
            if(this.config.selectedVoicesMultiple) this.selectedVoicesMultiple = this.config.selectedVoicesMultiple;
            if(this.config.selectedVoiceIds) this.selectedVoiceIds = this.config.selectedVoiceIds;
            if(this.config.voiceChangeInterval) this.voiceChangeInterval = this.config.voiceChangeInterval;
            if(this.config.superCloneIndexMap) this.superCloneIndexMap = this.config.superCloneIndexMap;
            if(this.config.voice) this.voice = this.config.voice; else this.voice=1579;
            if(this.config.temperature) this.temperature = this.config.temperature; else this.temperature='0.3';
            if(this.config.speed) this.speed = this.config.speed; else this.speed='1.0';
            if(this.config.prompt) this.prompt = this.config.prompt; else this.prompt = '[oral_3][laugh_1]';
            if(this.config.ignore_users) this.ignore_users = this.config.ignore_users;
        if(this.config.replyTemplate) this.replyTemplate = this.config.replyTemplate;
            if(this.config.forbiddenWords) this.forbiddenWords = this.config.forbiddenWords;
            this.fetchAudioList();
            this.playNext();
            this.startReading();
        this.sentencesInit();
            this.fetchSessions(); // 加载当前会话
            
            // 如果启用了轮班模式，优先设置当前声音，并按需启动声音轮换
            if (this.advancedVoiceMode && this.selectedVoicesMultiple.length > 0) {
                // 立即设置当前声音为所选列表的第一个
                this.currentVoiceIndex = 0;
                this.selectedRefAudio = this.selectedVoicesMultiple[0];
                if (this.selectedVoicesMultiple.length > 1) {
                    this.startVoiceRotation();
                }
            }
			setInterval(() => this.heartbeatCheck('playNext', 150000), 5000);
            setInterval(() => this.heartbeatCheck('startReading',100000), 5000);
            setInterval(() => this.heartbeatCheck('sentencesInit',150000), 5000);
            // 计算试用期剩余天数和今日剩余时间
            if(this.in_trial) {
                this.trialDaysLeft = Math.ceil((this.trial_expired - Date.now()/1000) / (24*3600));
                this.trialTimeLeft = Math.floor((1800 - this.trial_daily_usage) / 60);
            }
            // 初始化 AudioContext
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.previewAudioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 添加空格键控制播放/暂停的事件监听
            document.addEventListener('keydown', this.handleKeyDown);
        },
      beforeDestroy() {
        // 移除 beforeunload 事件监听
        window.removeEventListener('beforeunload', this.handlePageRefreshOrClose);
        document.removeEventListener('keydown', this.handleKeyDown);
        
        // 停止进度定时器
        if (this.progressTimer) {
            clearInterval(this.progressTimer);
            this.progressTimer = null;
        }
        
        // 停止声音轮换定时器
        this.stopVoiceRotation();
        
        // 关闭 AudioContext
        if (this.audioContext && this.audioContext.state !== 'closed') {
            this.audioContext.close();
        }
        if (this.previewAudioContext && this.previewAudioContext.state !== 'closed') {
            this.previewAudioContext.close();
        }
        // 移除拖动相关的事件监听器
        document.removeEventListener('mousemove', this.handleProgressMouseMove);
        document.removeEventListener('mouseup', this.handleProgressMouseUp);
        document.removeEventListener('touchmove', this.handleProgressMouseMove);
        document.removeEventListener('touchend', this.handleProgressMouseUp);
      },
        methods: {
          toggleAdvancedSettings() {
            this.showAdvancedSettings = !this.showAdvancedSettings;
          },
          // 连接公屏按钮点击处理函数
          connectLiveScreen() {
              // 获取平台对应的js代码
              fetch('/index/ailive2/getJsCode?platform='+this.platform)
                  .then(response => response.json())
                  .then(data => {
                      if (data.success) {
                          // 替换变量
                          let code = data.code;
                          code = code.replace(/{web_id}/g, this.web_id);
                          code = code.replace(/{platform}/g, this.platform);
                          code = code.replace(/{domain}/g, window.location.host);
                          this.jsCode = code;
                          // 显示房间ID输入弹窗
                          this.showRoomIdModal = true;
                      } else {
                          this.showToast(data.message || '获取连接脚本失败', 'error');
                      }
                  })
                  .catch(error => {
                      console.error('请求js代码出错:', error);
                      this.showToast('获取连接脚本失败，请重试', 'error');
                  });
          },
        handlePageRefreshOrClose(event) {
          if(this.sessionToken!='') this.endSession(this.sessionToken);
        },
        queueReset(){
          this.audioQueue={reply:[],welcome:[],insert:[],main:[]};
          this.sentences = [];
          this.sentences_prepare = [];
          this.live_messages={comment:[],room_enter:[],room_follow:[],room_like:[]};
          this.audioCache1 = []; // 清空存储池
          this.audioCache2 = []; // 清空播放池
          this.longStopMode = false; // 重置长停顿模式
          // 清除定时器
          if(this.playNextTimer) {
              clearTimeout(this.playNextTimer);
              this.playNextTimer = null;
          }
        },
        ccpCharge(){
          const computingCode = prompt('请输入算力码');
      	  if(!computingCode) return;
          // 发送 POST 请求到服务器
          fetch('/index/ailive2/ccpCharge', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ computingCode })
          })
         .then(response => response.json())
         .then(result => {
          // 处理服务器返回的结果
          if (result.success) {
            alert(result.message);
            this.ccp_remain = result.ccp_remain;
          } else {
            alert('充值失败：' + result.message);
          }
          })
         .catch(error => {
          alert('请求出错：' + error);
          });
        },
        activeVIP(){
          const activationCode = prompt('请输入激活码');
      	  if(!activationCode) return;
          // 发送 POST 请求到服务器
          fetch('/index/ailive2/vipActivation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ activationCode })
          })
         .then(response => response.json())
         .then(result => {
          // 处理服务器返回的结果
          if (result.success) {
            alert(result.message);
            this.ai_live_right = 1;
          } else {
            alert('激活失败：' + result.message);
          }
          })
         .catch(error => {
          alert('请求出错：' + error);
          });
        },
        heartbeatCheck(func_name,misstime){
          if(Date.now() - this.heartbeat[func_name] >misstime ) this[func_name](); //心跳100秒没跳了，就重新激活
        },
          // 切换会话列表显示状态
          toggleSessionList() {
              this.sessionListVisible = !this.sessionListVisible;
          },
          // 获取当前用户的会话列表
          async fetchSessions() {
              const response = await axios.get('/index/ailive2/getSessionList');
              this.activeSessions = response.data.sessions;
          },
          async endSessionX(sessionToken) {
          if(confirm("关闭后该会话讲停止运行，请谨慎操作"))
          {
            await this.endSession(sessionToken); 
          }
          },
          // 结束指定会话
          async endSession(sessionToken) {
          await axios.post('/index/ailive2/endSession', { 
              session_token: sessionToken,
              web_id: this.web_id 
          });
          this.fetchSessions(); // 刷新会话列表
          },
          async RunStop(){
              if(this.running==false) //处于停止状态
              {
                  if(this.computilityType==1 && this.self_tts_host=='')
                  {
                      alert('您勾选的是本地算力模式，请填写算力主机地址！');
                      return;
                  }
    
                  // 发送请求到后端以创建会话
            this.buttonTxt='学习中...';
                  const response = await axios.post('/index/ailive2/createSession',{
                      open_id: this.open_id,
                      room_id: this.room_id,  // 添加room_id
                      computilityType: this.computilityType,
                      self_tts_host: this.computilityType==1?this.self_tts_host:'',
                      entire_text: this.entire_text,
                      extract_info: this.extract_info,
                      info_text: this.info_text,
                      selectedRefAudio: this.selectedRefAudio,
                      rewriteMode: this.rewriteMode,
                      welcomeFlag: this.welcomeFlag,
                      replyFlag: this.replyFlag,
                      speed: this.speed,
                      ignore_users: this.ignore_users,
                      replyTemplate: this.replyTemplate,
                      selectedRefAssiAudio: this.selectedRefAssiAudio,
                      forbiddenWords: this.forbiddenWords,
                      web_id: this.web_id,
					  platform: this.platform,
                      // 轮班模式相关参数
                      advancedVoiceMode: this.advancedVoiceMode,
                      selectedVoiceIds: this.selectedVoiceIds,
                      voiceChangeInterval: this.voiceChangeInterval
                  });

                  if (response.data.status === 'error') {
                      alert(response.data.message);
                      this.fetchSessions(); // 刷新会话列表
					  if(response.data.code=='100')
					  {
                      	this.sessionListVisible = true; // 展开会话列表
					  }
					  this.buttonTxt='启动ai主播';
                      return;
                  }
                  
                  const token = response.data.token; // 获取新创建的会话token
    
                  this.sessionToken = token; // 保存token
            
            this.extract_info = response.data.extract_info;
    
                  this.showTopMessage('开始运行');
                  this.running = true;
                  this.buttonColor = '#95EC69';
                  this.buttonTxt='停止运行';
                  if(this.rewriteMode==3)
                  {
                    this.currentT = '接待中...';
                  }
                  else
                  {
                    this.currentT = '组织语言中...（稍等1分钟）';
                  }
                  this.isSystemMessage = true; // 设置为系统消息
            
                  // 启动后立即检查消息获取类型
                  this.checkMessageGetType();
              }
              else //处于运行状态
              {
                  this.showTopMessage('停止运行');
                  this.running = false;
                  
                  // 清除消息获取定时器
                  if(this.messageIntervalId) {
                      clearInterval(this.messageIntervalId);
                      this.messageIntervalId = null;
                  }
                  
                  await this.endSession(this.sessionToken);
                  this.sessionToken = '';
                  this.buttonColor = '';
                  this.buttonTxt='启动ai主播';
                  this.currentT = '';
				  //this.current_wavurl = '';
                  this.queueReset();
                  if (this.currentSourceNode) {
                    try {
                        this.currentSourceNode.stop();
                        this.currentSourceNode.disconnect();
                    } catch(e){}
                    this.currentSourceNode = null;
                    this.playNext(); // 显式启动播放循环
                  }
              }
          },
          onTextChange() {
          },
          showTopMessage(msg, type = 'info') {
            // 根据不同类型设置不同样式
            let tooltipClass = 'tooltip-' + type;
            this.tooltip = msg;
            this.tooltipShow = true;
            
            // 清除之前的定时器
            if (this.tooltipTimer) {
                clearTimeout(this.tooltipTimer);
            }
            
            // 3秒后自动隐藏
            this.tooltipTimer = setTimeout(() => {
                this.tooltipShow = false;
            }, 3000);
          },
          async textInsert(type){
              if(this.insertText.trim())
              {
          if(type==2)
          {
                  var sentence = {'type':'reply','text':this.insertText,'rewrite':'2','user':'','assistText':'','assistOverlap':0};
          }
		  else if(type==1)
		  {
			  var sentence = {'type':'insert','text':this.insertText,'rewrite':'1','user':'','assistText':'','assistOverlap':0};
		  }
          else if(type==0)
          {
            var sentence = {'type':'insert','text':this.insertText,'rewrite':'0','user':'','assistText':'','assistOverlap':0}; 
          }
                this.showTopMessage('插入中');
                await this.readParagraph(sentence);
                this.showTopMessage('插入成功');
              }
          },
          async sentencesInit(){
            this.heartbeat.sentencesInit = Date.now();
            if(this.running==false) //处于停止状态，则1秒后再来
            {
                setTimeout(this.sentencesInit, 1000 );
                return; 
            }
        
            if(this.entire_text=='' || this.rewriteMode==3) return;
            if(this.running==false) return;
            if(this.sentences.length==0)
            {
                this.sentences = this.sentences_prepare;
                this.sentences_prepare = [];
            }
            if(this.sentences_prepare.length>0)
            {
                setTimeout(this.sentencesInit, 1000 );
                return;
            }
            if(this.rewriteMode==1) 
            {
                // 检查重试次数
                if(this.requestRetryCount >= 2) {
                    this.showTopMessage('重表多次失败，请使用Chrome浏览器访问', 'error');
                    this.requestRetryCount = 0;
                    return;
                }

                try {
                    const response = await fetch(
                        "/index/ailive2/sectionRewrite",
                        {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                            },
                            body: JSON.stringify({session_token: this.sessionToken}),
                        }
                    );

                    if (!response.ok) { // 检查状态码是否不是 2xx 系列
                        throw new Error(`HTTP 错误! 状态码: ${response.status}`);
                    }
                    const data = await response.json();
                    this.rewrite_entire_text = data.content;
                    this.requestRetryCount = 0; // 成功后重置重试计数
                } catch (error) {
                    console.error('请求出错:', error);
                    this.requestRetryCount++; // 失败次数加1
                }
            }
            else
            {
                this.rewrite_entire_text = this.entire_text;
            }
    
        // 用优化句子切分函数，并解析助播话术
        var s = this.splitAndOptimizeSentences(this.rewrite_entire_text); // 句子切分，每句20到60个字
        
        // 遍历每个优化后的句子
        for (let i in s) {
          if (s[i].trim()) {
            this.sentences_prepare.push({
              type: 'main',
              text: s[i],  // 主播话术
              rewrite: this.rewriteMode == 2 ? '1' : '0',
              user: '',
            });
          }
        }
    
        
            this.sentencesInit();
          },
        //定义一个函数来解析句子
        parseSentence(sentence){
          // 解析每个句子的助播和咬合时长信息
          const parsed = this.parseAssistContent(sentence.text);
          sentence.text = parsed.hostText;
          
          let matchResult = sentence.text.match(/{停[:：](\d+)}/);
          let stopTime = 0;
          if (matchResult && matchResult.length > 1) {
            // 提取出数字部分并转换为数字类型
            stopTime = parseInt(matchResult[1], 10);
            // 移除停顿占位符
            sentence.text = sentence.text.replace(/{停[:：]\d+}/g, "");
          }
          sentence.stopTime = stopTime;
          sentence.assistText = parsed.assistText;
          sentence.assistText = sentence.assistText.replace(/{停[:：]\d+}/g, "");
          
          sentence.assistOverlap = parsed.overlapDuration;
          return sentence;
        },
        // 定义一个函数来计算句子的字数
        countWords(sentence) {
          // 匹配中文字符、英文单词和阿拉伯数字
          const words = sentence.match(/[\u4e00-\u9fa5]|[a-zA-Z]+|\d/g);
          return words ? words.length : 0;
        },	  
        // 定义一个函数来分割和优化句子
        splitAndOptimizeSentences(text, _min = 20, _max = 60) {
          text = text.replace(/([。！？])(\{停[:：]\d+\})/, "$2$1");
          // 提前处理助播话术，替换为占位符以避免误切割
          let assistParts = [];
          let assistPattern = /\{助[:：](-?\d+):(.*?)\}|\{助[:：](.*?)\}/g;  // 扩展支持无咬合时间的格式
          let modifiedText = text.replace(assistPattern, (match, p1, p2, p3) => {
            if (p3 !== undefined) {


              // 如果是简化格式，播内容在 p3，默认咬合时间为 -200
              assistParts.push({ duration: -200, text: p3 });
            } else {
              // 正常格式的情况
              assistParts.push({ duration: p1, text: p2 });
            }
            return `###ASSIST_PLACEHOLDER_${assistParts.length - 1}###`;  // 替换为占位符
          });
        
          // 语言检测函数
          const detectLanguage = (text) => {
            // 移除助播占位符和特殊符号来检测语言
            const cleanText = text.replace(/###ASSIST_PLACEHOLDER_\d+###/g, '').replace(/\{[^}]+\}/g, '');
            
            if (!cleanText.trim()) return 'en'; // 默认英文
            
            // 各种语言字符的正则表达式
            const chineseChars = cleanText.match(/[\u4e00-\u9fff]/g) || [];
            const japaneseChars = cleanText.match(/[\u3040-\u309f\u30a0-\u30ff]/g) || []; // 平假名+片假名
            const koreanChars = cleanText.match(/[\uac00-\ud7af]/g) || [];
            const arabicChars = cleanText.match(/[\u0600-\u06ff]/g) || [];
            const russianChars = cleanText.match(/[\u0400-\u04ff]/g) || [];
            const thaiChars = cleanText.match(/[\u0e00-\u0e7f]/g) || [];
            
            const totalChars = cleanText.replace(/\s/g, '').length;
            
            if (totalChars === 0) return 'en';
            
            // 计算各语言字符比例
            const chineseRatio = chineseChars.length / totalChars;
            const japaneseRatio = japaneseChars.length / totalChars;
            const koreanRatio = koreanChars.length / totalChars;
            const arabicRatio = arabicChars.length / totalChars;
            const russianRatio = russianChars.length / totalChars;
            const thaiRatio = thaiChars.length / totalChars;
            
            // 按优先级检测语言
            if (chineseRatio > 0.3) return 'zh';        // 中文
            if (japaneseRatio > 0.3) return 'ja';       // 日语
            if (koreanRatio > 0.3) return 'ko';         // 韩语
            if (arabicRatio > 0.3) return 'ar';         // 阿拉伯语
            if (russianRatio > 0.3) return 'ru';        // 俄语
            if (thaiRatio > 0.3) return 'th';           // 泰语
            
            // 检测欧洲语言（西班牙语、法语、德语等特殊字符）
            const europeanChars = cleanText.match(/[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]/gi) || [];
            if (europeanChars.length / totalChars > 0.1) return 'es'; // 欧洲语言统一用英文句号
            
            // 默认英文（包括其他拉丁语系）
            return 'en';
          };
          
          // 根据语言获取相应的句号
          const getPeriod = (language) => {
            // 使用中文句号的语言：中文、日语
            if (language === 'zh' || language === 'ja') {
              return '。';
            }
            // 其他语言都使用英文句号：英语、韩语、西班牙语、法语、德语、俄语、阿拉伯语、泰语等
            return '.';
          };

          // 按标点符号分割文本并过滤掉空字符串
          //let sentences = modifiedText.split(/(?<!\d)\.|[!;。！；\n]+/).filter(Boolean);

          // 修改：先保护数字中的小数点，然后再分割句子
          let tempText = modifiedText.replace(/(\d+)\.(\d+)/g, '$1__DOT_MARKER__$2');
          let rawSentences = tempText.split(/[\.!;。！；\n]+/).filter(Boolean);
          let sentences = rawSentences.map(sentence => sentence.replace(/__DOT_MARKER__/g, '.'));

          // 用于存储优化后的句子
          let optimizedSentences = [];
        
          // 初始化临时句子用于合并短句
          let tempSentence = '';
        
          // 遍历每个句子
          for (let i = 0; i < sentences.length; i++) {
            // 去除句子前后的空格
            let sentence = sentences[i].trim();
        
            // 检查当前句子是否包含助播部分
            const hasAssistCurrent = sentence.includes('###ASSIST_PLACEHOLDER_');
        
            // 检查下一句是否存在助播部分（避免和下一句合并）
            const hasAssistNext = i + 1 < sentences.length && sentences[i + 1].includes('###ASSIST_PLACEHOLDER_');
        
              // 检查当前句子是否存在停顿要求
            const hasStopTime = /{停[:：]/.test(sentence);
            
            // 如果存在临时句子，尝试将其与当前句子合并
            if (tempSentence && !hasAssistCurrent && !hasAssistNext && !hasStopTime) {
              // 检查合并后的长度
              if ((this.countWords(tempSentence) + this.countWords(sentence)) <= _max) {
                // 合并临时句子和当前句子
                sentence = tempSentence + sentence;
                tempSentence = '';
              } else {
                // 如果合并后的长度超过_max，则将临时句子存储为优化后的句子
                optimizedSentences.push(tempSentence);
                tempSentence = '';
              }
            }
        
            // 检查当前句子的长度
            if (this.countWords(sentence) < _min && !hasAssistCurrent && !hasAssistNext) {
              // 检测句子语言并选择相应的句号
              const language = detectLanguage(sentence);
              const period = getPeriod(language);
              // 如果太短且不包含助播部分，并且下一句不包含助播，则暂存，等待合并
              tempSentence = sentence + period;
            } else {
              // 如果句子长度足够，或者包含助播部分，直接存储
              optimizedSentences.push(sentence);
            }
          }
        
          // 添加任何剩余的临时句子到优化后的句子中
          if (tempSentence) {
            optimizedSentences.push(tempSentence.trim());
          }
        
          // 恢复助播部分
          optimizedSentences = optimizedSentences.map(sentence => {
            return sentence.replace(/###ASSIST_PLACEHOLDER_(\d+)###/g, (match, p1) => {
              let assist = assistParts[parseInt(p1, 10)];
              return `{助:${assist.duration}:${assist.text}}`;  // 恢复助播话术
            });
          });
        
          return optimizedSentences;
        },	  
        
        parseAssistContent(sentence) {
          // 定义正则表达式匹配两种助播格式
          const assistPattern = /\{助[:：]([-]?\d+):(.*?)\}|\{助[:：](.*?)\}/;
          const match = sentence.match(assistPattern);
        
          if (match) {
            if (match[3] !== undefined) {
              // 处理简化格式：{助:<助播话术>}，默认合时长为 -200 毫秒
              return {
                hostText: sentence.replace(assistPattern, '').trim(),  // 主播话术
                assistText: match[3].trim(),  // 助播话术
                overlapDuration: -200  // 默认咬合时长 -200
              };
            } else {
              // 处理带咬合时长的格式
              return {
                hostText: sentence.replace(assistPattern, '').trim(),  // 主播话术
                assistText: match[2].trim(),  // 助播话术
                overlapDuration: parseInt(match[1], 10)  // 咬合时长
              };
            }
          } else {
            return {
              hostText: sentence,  // 如果没有助播，主播话术完整句子
              assistText: '',  // 助播话术为空
              overlapDuration: 0  // 没有咬合时长
            };
          }
        },
    
        
          async startReading() {
            this.heartbeat.startReading = Date.now();
            if(this.running==false) //处于停止状态，则1秒后再来
            {
                setTimeout(this.startReading, 1000 );
                return; 
            }
            
            if(this.live_messages.comment.length>0)//当互动队列里还有未处理完的互动信息
            {
                var ignore_users = this.ignore_users.split('|||');
                var comment = this.live_messages.comment.shift();
          comment.content = comment.content.replace(/\[([\u4e00-\u9fff]{1,4})\]/g, '');
          if(Date.now() - comment.addtime > 30000) //超过30秒就不要回复了
          {
            //
          }
          else if(comment.content!='' && !comment.content.includes('@') && !ignore_users.includes(comment.nick_name))
                {
                    var sentence = {'type':'reply','text':comment.content,'rewrite':'2','user':comment.nick_name ,'assistText':'','assistOverlap':0}; //rewrite=2 需要回复
                    await this.readParagraph(sentence);
                }
                this.startReading();
            }
            else if(this.live_messages.room_enter.length>0)//当欢迎队列还有未处理完的欢迎信息
            {
                var re = this.live_messages.room_enter.shift();
                const reg = /^[\u4e00-\u9fff]+$/;
                if(re.addtime - Date.now() > 5000) //超过5秒就不要了
                {
                    //
                }
                else if(reg.test(re.nick_name)&&re.nick_name.length>=2) //昵称全中文的欢迎
                {
                    if(Date.now() - this.last_welcome_time >60000) //上一次欢迎是60秒之前了
                    {
                        this.last_welcome_time = Date.now();
                        var sentence = {'type':'welcome','text':'欢迎{'+re.nick_name+'}','rewrite':'3','user':re.nick_name,'validtime':re.addtime+10000 ,'assistText':'','assistOverlap':0} //rewrite=3 需要欢迎
                        await this.readParagraph(sentence);
                    }
                }
                this.startReading();
            }
            else
            {
                if(this.audioQueue.main.length>2)//囤了2句语音就先缓缓
                {
                    setTimeout(this.startReading, 1000 );
                }
                else
                {
                    if(this.sentences.length==0) //没有句子可合成的了，则等1秒再来看看
                    {
                        setTimeout(this.startReading, 1000 );
                    }
                    else
                    {
                        var sentence = this.sentences.shift();
                        await this.readParagraph(sentence);
                        this.startReading();
                    }
                }
            }
          },
          async readParagraph(sentence) {

        sentence = this.parseSentence(sentence); //解析这个句子对象
            
            // 超级克隆逻辑：如果当前选中的声音有子声音，则轮换使用
            let currentVoiceMd5 = this.selectedRefAudio;

            // 检查当前选中的声音是否为超级声音
            const mainVoice = Object.values(this.audioList).find(audio => audio.voice_md5 === this.selectedRefAudio);
            const isSuperVoice = mainVoice && mainVoice.sub === -9;

            if (isSuperVoice) {
                // 如果缓存中已有子声音列表，则直接使用；否则先加载
                if (this.superCloneSubVoicesMap[this.selectedRefAudio]) {
                    this.superCloneSubVoices = this.superCloneSubVoicesMap[this.selectedRefAudio];
                } else {
                    await this.loadSuperCloneSubVoices(this.selectedRefAudio);
                }

                if (this.superCloneSubVoices.length > 0) {
                    // 使用 ref[i++] 算法轮换子声音
                    currentVoiceMd5 = this.superCloneSubVoices[this.superCloneIndex].voice_md5;
                    this.superCloneIndex = (this.superCloneIndex + 1) % this.superCloneSubVoices.length;

                    // 保存当前超级声音的子声音索引
                    this.superCloneIndexMap[this.selectedRefAudio] = this.superCloneIndex;
                }
            }
            
            try {
              const response = await fetch(
                "/index/ailive2/tts",
                {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify({
                      user:sentence.user,
                      rewrite:sentence.rewrite,
                      text: sentence.text,
                      assistText: sentence.assistText,
                      assistOverlap: sentence.assistOverlap,
                      voice_md5:currentVoiceMd5,
              assi_voice_md5:this.selectedRefAssiAudio,
                      speed:this.speed,
                      open_id:this.open_id,
                      room_id:this.room_id,
                      info_text:this.info_text,
                      self_tts_host:this.computilityType==1?this.self_tts_host:'',
                      session_token: this.sessionToken, // 发送token
                      web_id: this.web_id
                  }),
                }
              );
          
              if (!response.ok) { // 检查状态码是否不是 2xx 系列
                throw new Error(`HTTP 错误! 状态码: ${response.status}`);
              }
          
              const data = await response.json();
          this.ccp_remain = data.ccp_remain;
          if(data.status=='error') //token失效了 就给它暂停掉
          {
            if(this.running == true)
            {
              await this.RunStop();
              alert(data.message);
            }
            return;
          }
          
              //请求有结果，则推入队列
              if(data.audio) 
              {
            data.sentence = sentence;
                  await this.pushObjToAudioQueue(data,sentence.type);
              }
              
            } catch (error) {
              console.error('请求出错:', error);
            }
          },
          async pushObjToAudioQueue(data,sentence_type) {
          var url = data.audio;
            try {
          // if(data.dtype=='base64')
          // {
          //   data.blobWavUrl = 'data:'+data.ftype+';base64,'+data.audio;
          // }
          // else
          // {
          //   const response = await fetch(url);
          //   if (!response.ok) {
          //   throw new Error(`HTTP 错误！状态: ${response.status}`);
          //   }
          //   const blob = await response.blob();
          //   data.blobWavUrl = URL.createObjectURL(blob);
          // }
              
          // if(data.blobWavUrl == null) return;

          let audioBufferData;
          if (data.dtype == 'base64') {
              const byteString = atob(data.audio);
              const ia = new Uint8Array(byteString.length);
              for (let i = 0; i < byteString.length; i++) {
                  ia[i] = byteString.charCodeAt(i);
              }
              audioBufferData = ia.buffer; // This is an ArrayBuffer
          } else {
              const response = await fetch(url);
              if (!response.ok) {
                  throw new Error(`HTTP 错误！状态: ${response.status}`);
              }
              audioBufferData = await response.arrayBuffer(); // Get ArrayBuffer directly
          }

          if (audioBufferData == null) return;
          
          data.arrayBuffer = audioBufferData; // Store ArrayBuffer for Web Audio API

          this.audioQueue[sentence_type].push(data); 
            } catch (error) {
              console.error('获取 WAV 资源时出错:', error);
              return null;
            }
          },
          async readLiveMessages() {
            if(this.running==false) return;
            if(!this.welcomeFlag && !this.replyFlag) return;

            try {
                // 根据消息获取类型选择不同的API端点
                const endpoint = this.msggetType === 2 ? 
                    "/index/ailive2/getLiveMessageIn" : 
                    "/index/ailive2/getLiveMessage";
                    
                const response = await fetch(
                    endpoint,
                    {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({ 
                            open_id: this.open_id,
                            room_id: this.room_id,
                            cursor: this.message_cursor, // 使用数据库时间戳游标
                            session_token: this.sessionToken,
                            web_id: this.web_id
                        }),
                    }
                );
                
                if (!response.ok) {
                    throw new Error(`HTTP 错误! 状态码: ${response.status}`);
                }
                
                const data = await response.json();
                if(data.data.room_id) this.room_id = data.data.room_id;
                if(data.data.cursor) this.message_cursor = data.data.cursor; // 更新数据库时间戳游标
                if(this.replyFlag&&data.data.messages.comment)
                {
                    data.data.messages.comment.forEach(message => {
                        if (!this.live_messages.comment.some(existingMessage => (existingMessage.msg_id === message.msg_id || (existingMessage.nick_name === message.nick_name && Math.abs(Date.now() - existingMessage.addtime) <= 60000)))) { //消息已存在  或者已存在同一个用户
                            message.addtime = Date.now();
                            this.live_messages.comment.push(message);
                        }
                    });
                }
                if(this.welcomeFlag&&data.data.messages.room_enter) //理论上每个都欢迎，但是如果已经超过10秒了，就不欢迎了
                {
                    data.data.messages.room_enter.forEach(message => {
                        if (!this.live_messages.room_enter.some(existingMessage => existingMessage.msg_id === message.msg_id)) {
                            message.addtime = Date.now();
                            this.live_messages.room_enter.push(message);
                        }
                    });
                }
                
            } catch (error) {
                console.error('请求公屏消息出错:', error);
            }
        },
          playEnd(){
          //this.currentT = this.date(Date.now()/1000)+' loading...'; 
          if(this.currentStopTime>0) //刚播完的这句需要停顿
          {
            if(this.currentStopTime > 5000) {
              // 超过5秒的停顿时，设置一个标志并立即调用playNext
              // 但只处理非主线话术(插入、回复、欢迎)
              this.longStopMode = true;
              this.playNext();
              // 同时设置主线话术的定时器
              setTimeout(() => {
                this.longStopMode = false;
                this.playNext();
              }, this.currentStopTime);
            } else {
              // 5秒内的停顿正常处理
              setTimeout(this.playNext, this.currentStopTime);
            }
          }
          else
          {
            this.playNext();
          }
          },
          playNext() {
            // 清除之前的定时器
            if(this.playNextTimer) {
                clearTimeout(this.playNextTimer);
                this.playNextTimer = null;
            }

            this.heartbeat.playNext = Date.now();
            if(this.running==false) this.audioQueue={reply:[],welcome:[],insert:[],main:[]}; //如果当前是处于停止状态，队列持续处于空的状态

            let q = null;
            if(this.audioQueue.insert.length) {
                q = this.audioQueue.insert.shift();
            } else if(this.audioQueue.reply.length) {
                q = this.audioQueue.reply.shift();
            } else if(this.audioQueue.welcome.length) {
                q = this.audioQueue.welcome.shift();
            } else if(!this.longStopMode) {
                // 只有在非长停顿模式下才处理主线话术
                // 合并所有主线话术处理逻辑
                if(this.audioQueue.main.length) {
                    q = this.audioQueue.main.shift();
                    // 更新存储池（cache1）
                    if (this.audioCache1.length >= 100) {
                        this.audioCache1.shift(); // 移除最旧的一句
                    }
                    this.audioCache1.push(q);
                } else {
                    // 主队列为空，检查是否应循环或等待
                    if (this.audioCache1.length>=10) {
                        // 缓存池有，尝试临时使用缓存
                        if (this.audioCache2.length === 0 ) {
                            // 仅当cache2为空时，才从audioCache1重新填充audioCache2
                            this.audioCache2 = [...this.audioCache1];
                        }
                        if (this.audioCache2.length > 0) {
                            q = this.audioCache2.shift(); // 从循环缓存播放
                        }
                    }
                    // 如果脚本未完成（sentences或sentences_prepare中有项目），
                    // 或缓存为空，q将保持为null，playNext将通过setTimeout再次调用。
                    // 这为startReading/readParagraph填充audioQueue.main留出了时间。
                }
            }

            // if(q) {
            //     this.current_wavurl = q.blobWavUrl;
            //     this.currentT = q.text; 
            //     this.currentStopTime = q.sentence.stopTime;
            // } else {
            //     this.playNextTimer = setTimeout(this.playNext, 1000);
            // }

            if (q && q.arrayBuffer) {
                this.currentT = q.text;
                this.currentStopTime = q.sentence.stopTime;
                this.isSystemMessage = false; // TTS语音内容，而非系统消息

                if (this.audioContext.state === 'suspended') {
                    this.audioContext.resume().catch(e => console.error("Error resuming audio context", e));
                }

                try {
                    // 使用slice()创建ArrayBuffer的副本，避免某些浏览器解码问题
                    const bufferToDecode = q.arrayBuffer.slice(0);
                    this.audioContext.decodeAudioData(bufferToDecode, 
                        (buffer) => {
                            if (this.currentSourceNode) {
                                try {
                                    this.currentSourceNode.stop();
                                    this.currentSourceNode.disconnect();
                                } catch (e) {
                                     // console.warn("Error stopping previous source:", e);
                                }
                            }
                            const source = this.audioContext.createBufferSource();
                            source.buffer = buffer;
                            source.connect(this.audioContext.destination);
                            source.onended = () => {
                                if (this.currentSourceNode === source) { // 确保是当前节点结束
                                    this.currentSourceNode = null;
                                    this.playEnd(); // 调用 playEnd 来处理后续逻辑，如播放下一个
                                }
                            };
                            source.start(0);
                            this.currentSourceNode = source;
                            // this.current_wavurl = ''; // 不再需要此变量来驱动播放
                            
                            // 重置暂停状态
                            this.isPaused = false;
                            
                            // 设置当前音频时长和开始计时
                            this.currentAudioDuration = buffer.duration;
                            this.playStartTime = this.audioContext.currentTime;
                            
                            // 启动进度更新定时器
                            this.startProgressTimer();
                            
                            // 保存当前buffer用于可能的下载
                            this.currentBuffer = buffer;
                        }, 
                        (e) => {
                            console.error("Error decoding audio data", e);
                            this.currentT = '音频解码失败';
                            this.isSystemMessage = true; // 系统错误消息
                            this.playNextTimer = setTimeout(this.playNext, 100); 
                        }
                    );
                } catch (e) {
                    console.error("Error in playNext with Web Audio API:", e);
                    this.currentT = '音频播放准备失败';
                    this.isSystemMessage = true; // 系统错误消息
                    this.playNextTimer = setTimeout(this.playNext, 100);
                }
            } else {
                 this.playNextTimer = setTimeout(this.playNext, 1000);
            }
        },
          configUpt(k,v) {
              this.config[k]=v;
              // 同时保存超级声音索引映射
              this.config.superCloneIndexMap = this.superCloneIndexMap;
              window.localStorage.setItem("config", JSON.stringify(this.config));
          },
          async fetchAudioList() {
              try {
                  const response = await fetch('/index/ailive2/promptVoiceList/?j=1');
                  if (response.ok) {
                      this.audioList = await response.json();
                      
                      // 兼容性处理：如果配置中有MD5数组但没有ID数组，则根据MD5生成ID数组
                      if (this.selectedVoicesMultiple.length > 0 && this.selectedVoiceIds.length === 0) {
                          this.selectedVoiceIds = this.selectedVoicesMultiple.map(md5 => {
                              const voice = Object.values(this.audioList).find(audio => audio.voice_md5 === md5);
                              return voice ? voice.id : null;
                          }).filter(id => id !== null);
                      }
                  } else {
                      console.error('获取音频列表失败');
                  }
              } catch (error) {
                  console.error('请求音频列表出错', error);
              }
          },
        date: function(timestamp,format='YY-MM-DD hh:mm:ss'){
          var date = new Date(timestamp*1000);
         
          var year = date.getFullYear(),
            month = date.getMonth()+1,//月份是从0开始的
            day = date.getDate(),
            hour = date.getHours(),
            min = date.getMinutes(),
            sec = date.getSeconds();
          var preArr = Array.apply(null,Array(10)).map(function(elem, index) {
            return '0'+index;
          });//开个长度为10的数组 格式为 ["00", "01", "02", "03", "04", "05", "06", "07", "08", "09"]
         
          var newTime = format.replace(/YY/g,year)
            .replace(/MM/g,preArr[month]||month)
            .replace(/DD/g,preArr[day]||day)
            .replace(/hh/g,preArr[hour]||hour)
            .replace(/mm/g,preArr[min]||min)
            .replace(/ss/g,preArr[sec]||sec);
         
          return newTime;

        },
        getRandomDelay: function (min,max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
        },
        //形调和
        bap: function () {
        this.ba[0] = new Audio('/static/music/4dl0mne91.mp3');
        this.ba[1] = new Audio('/static/music/b93lty782.mp3');
        this.ba[0].loop = true;
        this.ba[0].muted = true;
        this.ba[0].preload = 'auto';
        this.ba[1].loop = true;
        this.ba[1].muted = true;
        this.ba[1].preload = 'auto';
        this.ba[0].addEventListener('loadedmetadata', () => {
          const randomTime1 = Math.random() * this.ba[0].duration;  
          this.ba[0].currentTime = randomTime1;  
          console.log(`${randomTime1.toFixed(2)}`);
        });
        this.ba[1].addEventListener('loadedmetadata', () => {
          const randomTime2 = Math.random() * this.ba[1].duration;  
          this.ba[1].currentTime = randomTime2; 
          console.log(`${randomTime2.toFixed(2)}`);
        });
        document.addEventListener('click', () => {
          this.ba[0].muted = false;
          this.ba[1].muted = false;
          this.ba[0].play().catch((error) => {
          console.error('this.ba[0] playback failed:', error);
          });
          this.ba[1].play().catch((error) => {
          console.error('this.ba[1] playback failed:', error);
          });
        }, { once: true }); 
        },
        async selectVoice(voiceMd5) {
            // 如果点击的是当前已选中的声音，则取消选择
            if (this.selectedRefAudio === voiceMd5) {
                this.selectedRefAudio = '';
                this.superCloneSubVoices = [];
                this.superCloneIndex = 0;
            } else {
                this.selectedRefAudio = voiceMd5;
            }
            this.showVoiceModal = false;
        },
        
        // 轮班模式相关方法
        async toggleAdvancedVoiceMode() {
            if (this.advancedVoiceMode) {
                // 进入轮班模式时，初始化多选列表
                if (this.selectedRefAudio) {
                    this.selectedVoicesMultiple = [this.selectedRefAudio];
                    // 同时初始化ID数组
                    const currentVoice = Object.values(this.audioList).find(audio => audio.voice_md5 === this.selectedRefAudio);
                    this.selectedVoiceIds = currentVoice && currentVoice.id ? [currentVoice.id] : [];
                } else {
                    this.selectedVoicesMultiple = [];
                    this.selectedVoiceIds = [];
                }
            } else {
                // 退出轮班模式时，清理定时器和超级声音数据
                this.stopVoiceRotation();
                this.selectedVoicesMultiple = [];
                this.selectedVoiceIds = [];

            }
        },
        
        // 处理声音选择（兼容普通模式和轮班模式）
        handleVoiceSelect(voiceMd5) {
            if (this.advancedVoiceMode) {
                this.toggleVoiceSelection(voiceMd5);
            } else {
                this.selectVoice(voiceMd5);
            }
        },
        
        // 切换声音选择状态（轮班模式）
        toggleVoiceSelection(voiceMd5) {
            const index = this.selectedVoicesMultiple.indexOf(voiceMd5);
            const voice = Object.values(this.audioList).find(audio => audio.voice_md5 === voiceMd5);
            
            if (index > -1) {
                // 移除选择
                this.selectedVoicesMultiple.splice(index, 1);
                if (voice && voice.id) {
                    const idIndex = this.selectedVoiceIds.indexOf(voice.id);
                    if (idIndex > -1) {
                        this.selectedVoiceIds.splice(idIndex, 1);
                    }
                }
            } else {
                // 添加选择
                this.selectedVoicesMultiple.push(voiceMd5);
                if (voice && voice.id) {
                    this.selectedVoiceIds.push(voice.id);
                }
            }
            
            // 直接应用选择
            this.applyVoiceSelection();
        },
        
        // 获取声音项的样式类
        getVoiceItemClass(voiceMd5) {
            const classes = ['voice-item'];
            
            if (this.advancedVoiceMode) {
                if (this.selectedVoicesMultiple.includes(voiceMd5)) {
                    classes.push('selected-multiple');
                }
            } else {
                if (this.selectedRefAudio === voiceMd5) {
                    classes.push('active');
                }
            }
            
            return classes;
        },
        
        // 全选我的声音
        selectAllPersonalVoices() {
            Object.values(this.personalVoices).forEach(audio => {
                if (!this.selectedVoicesMultiple.includes(audio.voice_md5)) {
                    this.selectedVoicesMultiple.push(audio.voice_md5);
                    if (audio.id) {
                        this.selectedVoiceIds.push(audio.id);
                    }
                }
            });
            this.applyVoiceSelection();
        },
        
        // 全选公共声音
        selectAllPublicVoices() {
            Object.values(this.publicVoices).forEach(audio => {
                if (!this.selectedVoicesMultiple.includes(audio.voice_md5)) {
                    this.selectedVoicesMultiple.push(audio.voice_md5);
                    if (audio.id) {
                        this.selectedVoiceIds.push(audio.id);
                    }
                }
            });
            this.applyVoiceSelection();
        },
        
        // 清空所有选择
        clearAllSelectedVoices() {
            this.selectedVoicesMultiple = [];
            this.selectedVoiceIds = [];
            this.selectedRefAudio = '';
            this.stopVoiceRotation();
            this.showToast('已清空所有声音选择', 'info');
        },
        
        // 获取我的声音选择数量
        getSelectedPersonalCount() {
            return Object.values(this.personalVoices).filter(audio => 
                this.selectedVoicesMultiple.includes(audio.voice_md5)
            ).length;
        },
        
        // 获取公共声音选择数量
        getSelectedPublicCount() {
            return Object.values(this.publicVoices).filter(audio => 
                this.selectedVoicesMultiple.includes(audio.voice_md5)
            ).length;
        },
        
        // 应用声音选择
        async applyVoiceSelection() {
            if (this.selectedVoicesMultiple.length === 0) {
                this.selectedRefAudio = '';
                this.superCloneSubVoices = [];
                this.superCloneIndex = 0;
                this.stopVoiceRotation();
                return;
            }
            
            // 设置第一个选中的声音为当前声音
            this.selectedRefAudio = this.selectedVoicesMultiple[0];
            this.currentVoiceIndex = 0;
            
            
            // 如果选择了多个声音，启动自动切换
            if (this.selectedVoicesMultiple.length > 1) {
                this.startVoiceRotation();
                this.showToast(`已选择 ${this.selectedVoicesMultiple.length} 个声音，将每 ${this.voiceChangeInterval} 分钟自动切换`, 'success');
            } else {
                this.stopVoiceRotation();
                const isSuperVoice = this.superCloneSubVoices.length > 0;
                const message = isSuperVoice ? 
                    '声音设置已更新（超级声音将按句轮换子声音）' : 
                    '声音设置已更新';
                this.showToast(message, 'success');
            }
        },
        
        // 启动声音轮换
        startVoiceRotation() {
            this.stopVoiceRotation(); // 清除之前的定时器
            
            if (this.selectedVoicesMultiple.length <= 1) return;
            
            this.voiceChangeTimer = setInterval(async () => {
                this.currentVoiceIndex = (this.currentVoiceIndex + 1) % this.selectedVoicesMultiple.length;
                this.selectedRefAudio = this.selectedVoicesMultiple[this.currentVoiceIndex];
                
                
                // 获取声音名称
                const currentVoice = Object.values(this.audioList).find(audio => 
                    audio.voice_md5 === this.selectedRefAudio
                );
                const voiceName = currentVoice ? currentVoice.voice_name : '未知声音';
                
                // 如果是超级声音且有子声音，显示额外信息
                const isSuperVoice = currentVoice &&currentVoice.sub === -9;
                const message = isSuperVoice ? 
                    `已切换到超级声音：${voiceName}` : 
                    `已切换到声音：${voiceName}`;
                
                this.showToast(message, 'info');
                console.log('自动切换声音:', voiceName, isSuperVoice ? '(超级声音)' : '');
            }, this.voiceChangeInterval * 60 * 1000); // 转换为毫秒
        },
        
        // 停止声音轮换
        stopVoiceRotation() {
            if (this.voiceChangeTimer) {
                clearInterval(this.voiceChangeTimer);
                this.voiceChangeTimer = null;
            }
        },
        // 加载超级克隆的子声音列表
        async loadSuperCloneSubVoices(voiceMd5) {
            // 如果已经缓存，则直接使用缓存
            if (this.superCloneSubVoicesMap[voiceMd5]) {
                this.superCloneSubVoices = this.superCloneSubVoicesMap[voiceMd5];
                this.superCloneIndex = this.superCloneIndexMap[voiceMd5] || 0;
                return;
            }
            try {
                // 根据选中的主声音MD5，查找对应的主声音ID
                const mainVoice = Object.values(this.audioList).find(audio => audio.voice_md5 === voiceMd5);
                if (!mainVoice) {
                    this.superCloneSubVoices = [];
                    this.superCloneIndex = 0;
                    return;
                }
                
                // 请求子声音列表
                const response = await fetch('/index/ailive2/getSubVoices?parent_id=' + mainVoice.id);
                if (response.ok) {
                    const subVoices = await response.json();
                    if (subVoices.length > 0) {
                        // 包含主声音和所有子声音，用于轮换
                        this.superCloneSubVoices = [mainVoice, ...subVoices];
                        this.superCloneSubVoicesMap[voiceMd5] = this.superCloneSubVoices;
                        
                        // 恢复该超级声音之前的子声音索引，如果没有记录则从0开始
                        if (this.superCloneIndexMap[voiceMd5] !== undefined) {
                            this.superCloneIndex = this.superCloneIndexMap[voiceMd5];
                        } else {
                            this.superCloneIndex = 0;
                        }
                        
                        console.log('加载超级克隆子声音列表:', this.superCloneSubVoices.length, '个声音，当前索引:', this.superCloneIndex);
                    } else {
                        // 没有子声音，清空列表
                        this.superCloneSubVoices = [];
                        this.superCloneIndex = 0;
                    }
                } else {
                    this.superCloneSubVoices = [];
                    this.superCloneIndex = 0;
                }
            } catch (error) {
                console.error('加载子声音列表失败:', error);
                this.superCloneSubVoices = [];
                this.superCloneIndex = 0;
            }
        },
        toggleCollapse(type) {
            this.isCollapsed[type] = !this.isCollapsed[type];
        },
        previewVoice(audio) {
            if (this.currentPlayingVoice === audio.voice_md5) {
                // 停止播放
                if (this.previewSourceNode) {
                    try {
                        this.previewSourceNode.stop();
                        this.previewSourceNode.disconnect();
                    } catch(e){}
                    this.previewSourceNode = null;
                }
                this.currentPlayingVoice = '';
            } else {
                // 开始播放
                if (this.previewSourceNode) { // 停止上一个预览
                     try {
                        this.previewSourceNode.stop();
                        this.previewSourceNode.disconnect();
                    } catch(e){}
                }
                
                this.currentPlayingVoice = audio.voice_md5; // 先设置，表示正在加载/播放

                if (this.previewAudioContext.state === 'suspended') {
                    this.previewAudioContext.resume().catch(e => console.error("Error resuming preview audio context", e));
                }

                const fetchUrl = /^https?:\/\//.test(audio.voice_path) ? audio.voice_path : '/static/ref_voices/' + (audio.voice_path.startsWith('/')?audio.voice_path.replace(/^\/+/, ''):audio.voice_path);
                fetch(fetchUrl)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.arrayBuffer();
                    })
                    .then(arrayBuffer => {
                        this.previewAudioContext.decodeAudioData(arrayBuffer, 
                            (buffer) => {
                                const source = this.previewAudioContext.createBufferSource();
                                source.buffer = buffer;
                                source.connect(this.previewAudioContext.destination);
                                source.onended = () => {
                                    if (this.previewSourceNode === source) {
                                        this.previewSourceNode = null;
                                        if (this.currentPlayingVoice === audio.voice_md5) {
                                           this.currentPlayingVoice = '';
                                        }
                                    }
                                };
                                source.start(0);
                                this.previewSourceNode = source;
                            }, 
                            (e) => {
                                console.error('Error decoding preview audio data', e);
                                this.currentPlayingVoice = ''; 
                            }
                        );
                    })
                    .catch(error => {
                        console.error('Error fetching or playing preview audio:', error);
                        this.currentPlayingVoice = '';
                    });
            }
        },
        selectAssiVoice(voiceMd5) {
            // 如果点击的是当前已选中的声音，则取消选择
            if (this.selectedRefAssiAudio === voiceMd5) {
                this.selectedRefAssiAudio = '';
            } else {
                this.selectedRefAssiAudio = voiceMd5;
            }
            this.showAssiVoiceModal = false;
        },
        checkWordCount(field, limit) {
            const text = this[field];
            // 计算文本的单词数（针对英文）或字符数（针对中文）
            const wordCount = this.getWordCount(text);
            if (wordCount > limit) {
                // 如果超过限制，裁剪文本
                // 由于无法精确按单词数裁剪，这里采用逐步裁剪的方式
                let newText = text;
                while (this.getWordCount(newText) > limit && newText.length > 0) {
                    newText = newText.slice(0, newText.length - 1);
                }
                this[field] = newText;
            }
        },
        getWordCountClass(field, limit) {
            const text = this[field];
            const wordCount = this.getWordCount(text);
            if (wordCount > limit) return 'error';
            if (wordCount > limit * 0.9) return 'warning';
            return '';
        },
        // 根据文本内容判断是按字符数还是单词数计算
        getWordCount(text) {
            // 检查文本是否主要包含英文
            const isMainlyEnglish = /[a-zA-Z]/.test(text) && !/[\u4e00-\u9fa5]/.test(text);
            
            if (isMainlyEnglish) {
                // 英文文本：按单词数计算（去除多余空格并分割）
                const words = text.trim().split(/\s+/);
                return words.length > 0 ? words.length : 0;
            } else {
                // 中文或混合文本：按字符数计算
                return text.length;
            }
        },
        closePowerSettings() {
            this.showPowerSettings = false;
        },
        selectPowerType(type) {
			if(this.running) return false;
            this.computilityType = type;
        },
        togglePowerSettings() {
            this.showPowerSettings = !this.showPowerSettings;
        },
        
        // 播放/暂停切换
        togglePlayPause() {
            if (!this.running || !this.currentSourceNode) return;
            
            if (this.isPaused) {
                // 恢复播放
                if (this.audioContext.state === 'suspended') {
                    this.audioContext.resume();
                }
                this.isPaused = false;
                
                // 更新开始时间，考虑已经播放过的时间
                const elapsedTime = this.playProgress / 100 * this.currentAudioDuration;
                this.playStartTime = this.audioContext.currentTime - elapsedTime;
                
                // 重新启动进度定时器
                this.startProgressTimer();
            } else {
                // 暂停播放
                if (this.audioContext.state === 'running') {
                    this.audioContext.suspend();
                }
                this.isPaused = true;
                
                // 停止进度更新
                if (this.progressTimer) {
                    clearInterval(this.progressTimer);
                    this.progressTimer = null;
                }
            }
        },
        
        // 启动进度更新定时器
        startProgressTimer() {
            // 先清除可能存在的定时器
            if (this.progressTimer) {
                clearInterval(this.progressTimer);
            }
            
            // 设置新的定时器，每100毫秒更新一次进度
            this.progressTimer = setInterval(() => {
                if (!this.isPaused && this.currentAudioDuration > 0) {
                    const elapsed = this.audioContext.currentTime - this.playStartTime;
                    this.playProgress = Math.min(Math.max(0, (elapsed / this.currentAudioDuration) * 100), 100);
                    
                    // 如果进度到达100%，停止定时器
                    if (this.playProgress >= 100) {
                        clearInterval(this.progressTimer);
                        this.progressTimer = null;
                    }
                }
            }, 100);
        },
        
        // 格式化时间显示
        formatTime(progress, duration) {
            if (!duration) return '0:00 / 0:00';
            
            const currentSeconds = (progress / 100) * duration;
            const totalSeconds = duration;
            
            return this.secondsToTimeString(currentSeconds) + ' / ' + this.secondsToTimeString(totalSeconds);
        },
        
        // 将秒数转换为时间字符串 mm:ss
        secondsToTimeString(seconds) {
            const minutes = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return minutes + ':' + (secs < 10 ? '0' : '') + secs;
        },
        
        // 处理键盘事件
        handleKeyDown(e) {
            // 检查事件目标元素是否为输入类型元素（input、textarea等）
            const targetTag = e.target.tagName.toLowerCase();
            const isInputField = targetTag === 'input' || 
                                 targetTag === 'textarea' || 
                                 e.target.isContentEditable || 
                                 e.target.getAttribute('role') === 'textbox';
            
            // 如果目标是输入框，则不触发播放/暂停功能
            if (isInputField) {
                return;
            }
            
            // 如果按下空格键，切换播放/暂停状态
            if ((e.code === 'Space' || e.keyCode === 32) && this.running && this.currentT && !this.isSystemMessage) {
                // 防止空格键触发页面滚动
                e.preventDefault();
                this.togglePlayPause();
                
                // 触发播放控制提示的高亮显示
                const controlHint = document.querySelector('.control-hint');
                if (controlHint) {
                    controlHint.classList.add('hint-flash');
                    setTimeout(() => {
                        controlHint.classList.remove('hint-flash');
                    }, 1000);
                }
            }
        },
        
        // 下载当前播放的音频
        downloadCurrentAudio() {
            if (!this.currentBuffer || !this.currentT || this.isSystemMessage) return;
            
            // 创建一个离线的AudioContext
            const offlineCtx = new OfflineAudioContext(
                this.currentBuffer.numberOfChannels, 
                this.currentBuffer.length, 
                this.currentBuffer.sampleRate
            );
            
            // 创建一个新的buffer source
            const source = offlineCtx.createBufferSource();
            source.buffer = this.currentBuffer;
            source.connect(offlineCtx.destination);
            source.start(0);
            
            // 渲染音频
            offlineCtx.startRendering().then(renderedBuffer => {
                // 创建WAV文件
                const wavBlob = this.bufferToWave(renderedBuffer);
                
                // 创建下载链接
                const url = URL.createObjectURL(wavBlob);
                const a = document.createElement('a');
                a.href = url;
                // 使用当前文本的前20个字符作为文件名
                const fileName = 'ai-voice-' + 
                    this.currentT.substring(0, 20).replace(/[^\w\u4e00-\u9fa5]/g, '_') + 
                    '.wav';
                a.download = fileName;
                a.click();
                
                // 清理
                setTimeout(() => URL.revokeObjectURL(url), 100);
            }).catch(err => {
                console.error('下载音频失败:', err);
                this.showTopMessage('下载音频失败', 'error');
            });
        },
        
        // 将AudioBuffer转换为WAV格式
        bufferToWave(buffer) {
            const numOfChan = buffer.numberOfChannels;
            const length = buffer.length * numOfChan * 2 + 44;
            const output = new ArrayBuffer(length);
            const view = new DataView(output);
            let offset = 0;
            let pos = 0;
            
            // 写入WAV头部
            // "RIFF"
            setUint32(0x46464952);
            // 文件长度
            setUint32(length - 8);
            // "WAVE"
            setUint32(0x45564157);
            // "fmt "
            setUint32(0x20746d66);
            // 格式块长度
            setUint32(16);
            // 格式类型 (PCM)
            setUint16(1);
            // 通道数
            setUint16(numOfChan);
            // 采样率
            setUint32(buffer.sampleRate);
            // 字节率 (采样率 * 每个样本的字节)
            setUint32(buffer.sampleRate * 2 * numOfChan);
            // 每个样本的字节 (通道数 * 每个样本位数 / 8)
            setUint16(numOfChan * 2);
            // 每个样本位数
            setUint16(16);
            // "data"
            setUint32(0x61746164);
            // 数据长度
            setUint32(buffer.length * numOfChan * 2);
            
            // 取样数据
            const channelData = [];
            let sample = 0;
            
            // 提取所有通道的数据
            for (let i = 0; i < numOfChan; i++) {
                channelData.push(buffer.getChannelData(i));
            }
            
            // 交错写入样本数据
            while (pos < buffer.length) {
                for (let i = 0; i < numOfChan; i++) {
                    sample = Math.max(-1, Math.min(1, channelData[i][pos]));
                    sample = (0.5 + sample < 0 ? sample * 32768 : sample * 32767) | 0;
                    view.setInt16(offset, sample, true);
                    offset += 2;
                }
                pos++;
            }
            
            // 辅助函数，用于设置数据
            function setUint16(data) {
                view.setUint16(offset, data, true);
                offset += 2;
            }
            
            function setUint32(data) {
                view.setUint32(offset, data, true);
                offset += 4;
            }
            
            return new Blob([output], { type: 'audio/wav' });
        },
        
        showToast(message, type = 'success') {
            this.toast.message = message;
            this.toast.type = type;
            this.toast.show = true;
            setTimeout(() => {
                this.toast.show = false;
            }, 3000);
        },
        claimTrial() {
            axios.post('/index/ailive2/claimTrial')
                .then(response => {
                    if(response.data.success) {
                        this.showToast(response.data.message, 'success');
                        // 更新用户状态
                        this.in_trial = true;
                        this.trial_expired = response.data.trial_expired;
                        this.trial_daily_usage = 0;
                        this.ccp_remain = this.ccp_remain + (1.5 * 3600); // 增加1.5小时云算力
                        // 刷新页面以更新状态
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        this.showToast(response.data.message, 'error');
                    }
                })
                .catch(error => {
                    this.showToast('领取失败,请稍后重试', 'error');
                });
        },
        startBroadcast() {
            this.RunStop();
        },
        async saveLiveMessages() {
            if(!this.running || !this.open_id) return;
            
            try {
                const response = await axios.post('/index/ailive2/saveLiveMessages', {
                    open_id: this.open_id,
                    room_id: this.room_id,
                    cursor: this.douyin_cursor, // 使用抖音接口游标
                    session_token: this.sessionToken,
                    web_id: this.web_id
                });
                
                // 更新抖音接口游标
                if(response.data.success === 1 && response.data.cursor) {
                    this.douyin_cursor = response.data.cursor;
                }
            } catch(error) {
                console.error('保存公屏信息失败:', error);
            }
        },
        copyJsCode() {
            const code = this.jsCode.replace('ROOM_ID', this.inputRoomId);
            const textArea = document.createElement('textarea');
            textArea.value = code;
            
            // 设置样式使文本域不可见
            textArea.style.position = 'fixed';
            textArea.style.top = '0';
            textArea.style.left = '0';
            textArea.style.width = '2em';
            textArea.style.height = '2em';
            textArea.style.padding = '0';
            textArea.style.border = 'none';
            textArea.style.outline = 'none';
            textArea.style.boxShadow = 'none';
            textArea.style.background = 'transparent';
            
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    this.showToast('代码已复制到剪贴板', 'success');
                } else {
                    this.showToast('复制失败，请手动复制', 'error');
                }
            } catch (err) {
                this.showToast('复制失败，请手动复制', 'error');
            }
            
            document.body.removeChild(textArea);
        },
        checkMessageGetType() {
            if(!this.sessionToken) {
                setTimeout(this.checkMessageGetType, 2000);
                return;
            }
            
            axios.post('/index/ailive2/checkMsggetType', {
                session_token: this.sessionToken
            })
            .then(response => {
                if(response.data.success) {
                    const newMsggetType = response.data.msgget_type;
                    
                    // 只有当消息获取类型发生变化时，才重新设置定时器
                    if(this.msggetType !== newMsggetType || !this.messageIntervalId) {
                        // 保存新的消息获取类型
                        this.msggetType = newMsggetType;
                        
                        // 清除现有的定时器
                        if(this.messageIntervalId) {
                            clearInterval(this.messageIntervalId);
                            this.messageIntervalId = null;
                        }
                        
                        // 根据消息获取类型设置不同的定时器频率
                        if(newMsggetType === 1 || newMsggetType === 2) {
                            // 设置定时器，根据类型决定频率
                            const interval = newMsggetType === 1 ? 5000 : 2000;
                            this.messageIntervalId = setInterval(() => {
                                if(this.running) this.readLiveMessages();
                            }, interval);
                        }
                    }
                    
                    // 每30秒重新检查一次消息获取类型
                    setTimeout(this.checkMessageGetType, 30000);
                } else {
                    // 如果检查失败，稍后重试
                    setTimeout(this.checkMessageGetType, 5000);
                }
            })
            .catch(error => {
                console.error('检查消息获取类型失败:', error);
                setTimeout(this.checkMessageGetType, 5000);
            });
        },
        // 新增：处理进度条拖动的方法
        handleProgressMouseDown(e) {
          if (!this.currentBuffer || this.isSystemMessage) return;
          
          this.isDraggingProgress = true;
          // 同时处理触摸事件
          if (e.type === 'touchstart') {
              document.addEventListener('touchmove', this.handleProgressMouseMove);
              document.addEventListener('touchend', this.handleProgressMouseUp);
          } else {
              document.addEventListener('mousemove', this.handleProgressMouseMove);
              document.addEventListener('mouseup', this.handleProgressMouseUp);
          }
          this.updateProgressFromEvent(e); // 立即根据点击位置更新一次
        },
        handleProgressMouseMove(e) {
          if (!this.isDraggingProgress) return;
          this.updateProgressFromEvent(e);
        },
        handleProgressMouseUp(e) {
          if (!this.isDraggingProgress) return;
          this.isDraggingProgress = false;
          
          if (e.type === 'touchend') {
              document.removeEventListener('touchmove', this.handleProgressMouseMove);
              document.removeEventListener('touchend', this.handleProgressMouseUp);
          } else {
              document.removeEventListener('mousemove', this.handleProgressMouseMove);
              document.removeEventListener('mouseup', this.handleProgressMouseUp);
          }
          // 可选：如果MouseMove中只是更新UI，在这里触发最终的seekAudio
          // this.updateProgressFromEvent(e, true); // 传入true表示这是最终的seek
        },
        updateProgressFromEvent(e, finalSeek = true) {
          const progressBar = this.$refs.progressBar;
          if (!progressBar || !this.currentAudioDuration) return;

          const rect = progressBar.getBoundingClientRect();
          const clientX = e.type.startsWith('touch') ? e.touches[0].clientX : e.clientX;
          let progress = (clientX - rect.left) / rect.width;
          progress = Math.max(0, Math.min(1, progress)); // 确保在0和1之间

          const newTime = progress * this.currentAudioDuration;

          if (this.isDraggingProgress && !finalSeek) {
               // 拖动过程中，只更新进度条显示，不实际seek，以提高性能
               this.playProgress = progress * 100;
          } else {
              this.seekAudio(newTime);
          }
        },
        seekAudio(newTime) {
          if (!this.currentBuffer || this.isSystemMessage || newTime === undefined || newTime === null || isNaN(newTime)) {
              console.warn('Seek aborted:', { hasBuffer: !!this.currentBuffer, isSystem: this.isSystemMessage, time: newTime });
              return;
          }

          // 停止当前可能正在更新进度的定时器
          if (this.progressTimer) {
              clearInterval(this.progressTimer);
              this.progressTimer = null;
          }

          const wasPaused = this.isPaused;
          if (this.currentSourceNode) {
              this.currentSourceNode.onended = null; // 清除旧的回调，防止playEnd意外触发
              try {
                  this.currentSourceNode.stop();
                  this.currentSourceNode.disconnect();
              } catch (err) {
                  // console.warn("Error stopping current source node during seek:", err);
              }
              this.currentSourceNode = null; 
          }

          if (this.audioContext.state === 'suspended') {
               this.audioContext.resume().catch(e => console.error("Error resuming audio context for seek", e));
          }
          
          const source = this.audioContext.createBufferSource();
          source.buffer = this.currentBuffer;
          source.connect(this.audioContext.destination);
          
          // 设置新的 onended 处理函数
          source.onended = () => {
              if (this.currentSourceNode === source) { // 确保是当前节点结束
                  this.currentSourceNode = null;
                  // 如果不是因为拖拽导致的停止（比如自然播放结束），则调用 playEnd
                  if (!this.isDraggingProgress) { 
                      this.playEnd();
                  }
              }
          };
          
          // 确保 newTime 在有效范围内
          newTime = Math.max(0, Math.min(newTime, this.currentAudioDuration));

          try {
              source.start(0, newTime);
          } catch (error) {
              console.error("Error starting audio source at new time:", error, "newTime:", newTime, "duration:", this.currentAudioDuration);
              // 如果start失败，尝试恢复之前的状态或停止
              if (wasPaused) this.audioContext.suspend().catch(e => console.error("Error re-suspending audio context", e));
              return;
          }
          
          this.currentSourceNode = source;
          this.playStartTime = this.audioContext.currentTime - newTime; // 更新播放开始的上下文时间
          this.playProgress = (newTime / this.currentAudioDuration) * 100; // 立刻更新进度条显示
          
          this.isPaused = false; // 先假设播放开始
          if (wasPaused) {
              // 如果之前是暂停状态，并且音频上下文当前是running, 则重新挂起它
              if (this.audioContext.state === 'running') {
                  this.audioContext.suspend().catch(e => console.error("Error re-suspending audio context", e));
              }
              this.isPaused = true; // 保持暂停状态
          }
          
          // 如果不是暂停状态，或者即使暂停了也应该启动计时器来更新UI
           this.startProgressTimer(); 
        },
        },
        watch: {
          entire_text: function(val){
			  if(val!=this.config.entire_text) this.extract_info = '';
              this.configUpt('entire_text',val);
          },
        extract_info: function(val){
              this.configUpt('extract_info',val);
          },
          info_text: function(val){
              this.configUpt('info_text',val);
          },
          selectedRefAudio: function(val){
              this.configUpt('selectedRefAudio',val);
          },
          selectedRefAssiAudio: function(val){
              this.configUpt('selectedRefAssiAudio',val);
          },
          self_tts_host: function(val){
              this.configUpt('self_tts_host',val);
          },
          computilityType: function(val){
              this.configUpt('computilityType',val);
          },
          voice: function(val){
              this.configUpt('voice',val);
          },
          prompt: function(val){
              this.configUpt('prompt',val);
          },
          temperature: function(val){
              this.configUpt('temperature',val);
          },
          speed: function(val){
              this.configUpt('speed',val);
          },
          ignore_users: function(val){
              this.configUpt('ignore_users',val);
          },
          replyTemplate: function(val){
              this.configUpt('replyTemplate',val);
          },
          forbiddenWords: function(val){
              this.configUpt('forbiddenWords',val);
          },
          // 轮班声音模式配置监听
          advancedVoiceMode: function(val){
              this.configUpt('advancedVoiceMode',val);
          },
          selectedVoicesMultiple: function(val){
              this.configUpt('selectedVoicesMultiple',val);
          },
          selectedVoiceIds: function(val){
              this.configUpt('selectedVoiceIds',val);
          },
          voiceChangeInterval: function(val){
              this.configUpt('voiceChangeInterval',val);
              // 如果正在进行声音轮换，重新启动以应用新的间隔
              if (this.voiceChangeTimer && this.selectedVoicesMultiple.length > 1) {
                  this.startVoiceRotation();
              }
          },
          currentT: {
              handler(newVal) {
                  if (newVal && this.running) {
                      this.showCurrentText = true;
                      // 可选：添加自动隐藏
                      /* setTimeout(() => {
                          this.showCurrentText = false;
                      }, 5000); */
                  }
              },
              immediate: true
          }
        },
        filters: {
          formatTime: function(timestamp) {
              const date = new Date(timestamp * 1000);
              return `${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`;
          }
        },
        directives: {
            'click-outside': {
                bind(el, binding) {
                    el.clickOutsideEvent = function(event) {
                        if (!(el === event.target || el.contains(event.target))) {
                            binding.value(event);
                        }
                    };
                    document.addEventListener('click', el.clickOutsideEvent);
                },
                unbind(el) {
                    document.removeEventListener('click', el.clickOutsideEvent);
                }
            }
        },
        computed: {
            // 实时计算剩余天数
            trialDaysLeft() {
                if(!this.in_trial) return 0;
                return Math.ceil((this.trial_expired - Date.now()/1000) / (24*3600));
            },
            // 实时计算今日剩余分钟
            trialTimeLeft() {
                if(!this.in_trial) return 0;
                return Math.floor((1800 - this.trial_daily_usage) / 60);
            },
            personalVoices() {
                // 包含普通主声音(sub==0)以及超级克隆主声音(sub==-9)
                return Object.values(this.audioList).filter(audio => audio.voice_type !== 9 && (audio.sub === undefined || audio.sub === null || audio.sub <= 0));
            },
            publicVoices() {
                return Object.values(this.audioList).filter(audio => audio.voice_type === 9);
            },
            // 获取选中声音的数量
            selectedVoicesCount() {
                return this.selectedVoicesMultiple.length;
            }
        }
      });
    </script>
    

</body></html>